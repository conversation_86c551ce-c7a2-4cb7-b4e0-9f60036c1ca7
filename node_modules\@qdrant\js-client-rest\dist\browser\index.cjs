'use strict';

var none = {
    isNone: function () {
        return true;
    },
    orElse: function (fallback) {
        return fallback;
    },
    orCall: function (getFallback) {
        return getFallback();
    },
    orNull: function () {
        return null;
    },
    orThrow: function (message) {
        if (message === void 0) { message = 'Unexpected null value'; }
        throw new TypeError(message);
    },
    map: function () {
        return none;
    },
    get: function () {
        return none;
    }
};
var Some = /** @class */ (function () {
    function Some(value) {
        this.value = value;
    }
    Some.prototype.isNone = function () {
        return false;
    };
    Some.prototype.orElse = function () {
        return this.value;
    };
    Some.prototype.orCall = function () {
        return this.value;
    };
    Some.prototype.orNull = function () {
        return this.value;
    };
    Some.prototype.orThrow = function () {
        return this.value;
    };
    Some.prototype.map = function (f) {
        return maybe(f(this.value));
    };
    Some.prototype.get = function (key) {
        return this.map(function (obj) { return obj[key]; });
    };
    return Some;
}());
function isMaybe(value) {
    return value === none || value instanceof Some;
}
function maybe(value) {
    if (isMaybe(value)) {
        return value;
    }
    if (value == null) {
        return none;
    }
    return some(value);
}
function some(value) {
    if (value == null) {
        throw new TypeError('some() does not accept null or undefined');
    }
    return new Some(value);
}

class ApiError extends Error {
    constructor(response) {
        super(response.statusText);
        Object.setPrototypeOf(this, new.target.prototype);
        this.headers = response.headers;
        this.url = response.url;
        this.status = response.status;
        this.statusText = response.statusText;
        this.data = response.data;
    }
}

let bigintReviver;
let bigintReplacer;
if ('rawJSON' in JSON) {
    bigintReviver = function (_key, val, context) {
        if (Number.isInteger(val) && !Number.isSafeInteger(val)) {
            try {
                return BigInt(context.source);
            }
            catch {
                return val;
            }
        }
        return val;
    };
    bigintReplacer = function (_key, val) {
        if (typeof val === 'bigint') {
            return JSON.rawJSON(String(val));
        }
        return val;
    };
}
const sendBody = (method) => method === 'post' ||
    method === 'put' ||
    method === 'patch' ||
    method === 'delete';
function queryString(params) {
    const qs = [];
    const encode = (key, value) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`;
    Object.keys(params).forEach((key) => {
        const value = params[key];
        if (value != null) {
            if (Array.isArray(value)) {
                value.forEach((value) => qs.push(encode(key, value)));
            }
            else {
                qs.push(encode(key, value));
            }
        }
    });
    if (qs.length > 0) {
        return `?${qs.join('&')}`;
    }
    return '';
}
function getPath(path, payload) {
    return path.replace(/\{([^}]+)\}/g, (_, key) => {
        const value = encodeURIComponent(payload[key]);
        delete payload[key];
        return value;
    });
}
function getQuery(method, payload, query) {
    let queryObj = {};
    if (sendBody(method)) {
        query.forEach((key) => {
            queryObj[key] = payload[key];
            delete payload[key];
        });
    }
    else {
        queryObj = { ...payload };
    }
    return queryString(queryObj);
}
function getHeaders(body, init) {
    const headers = new Headers(init);
    if (body !== undefined &&
        !(body instanceof FormData) &&
        !headers.has('Content-Type')) {
        headers.append('Content-Type', 'application/json');
    }
    if (!headers.has('Accept')) {
        headers.append('Accept', 'application/json');
    }
    return headers;
}
function getBody(method, payload) {
    if (!sendBody(method)) {
        return;
    }
    const body = payload instanceof FormData
        ? payload
        : JSON.stringify(payload, bigintReplacer);
    return method === 'delete' && body === '{}' ? undefined : body;
}
function mergeRequestInit(first, second) {
    const headers = new Headers(first?.headers);
    const other = new Headers(second?.headers);
    for (const key of other.keys()) {
        const value = other.get(key);
        if (value != null) {
            headers.set(key, value);
        }
    }
    return { ...first, ...second, headers };
}
function getFetchParams(request) {
    const payload = Object.assign(Array.isArray(request.payload) ? [] : {}, request.payload);
    const path = getPath(request.path, payload);
    const query = getQuery(request.method, payload, request.queryParams);
    const body = getBody(request.method, payload);
    const headers = sendBody(request.method)
        ? getHeaders(body, request.init?.headers)
        : new Headers(request.init?.headers);
    const url = request.baseUrl + path + query;
    const init = {
        ...request.init,
        method: request.method.toUpperCase(),
        headers,
        body,
    };
    return { url, init };
}
async function getResponseData(response) {
    if (response.status === 204) {
        return;
    }
    const contentType = response.headers.get('content-type');
    const responseText = await response.text();
    if (contentType && contentType.includes('application/json')) {
        return JSON.parse(responseText, bigintReviver);
    }
    try {
        return JSON.parse(responseText, bigintReviver);
    }
    catch (e) {
        return responseText;
    }
}
async function fetchJson(url, init) {
    const response = await fetch(url, init);
    const data = await getResponseData(response);
    const result = {
        headers: response.headers,
        url: response.url,
        ok: response.ok,
        status: response.status,
        statusText: response.statusText,
        data,
    };
    if (result.ok) {
        return result;
    }
    throw new ApiError(result);
}
function wrapMiddlewares(middlewares, fetch) {
    const handler = async (index, url, init) => {
        if (middlewares == null || index === middlewares.length) {
            return fetch(url, init);
        }
        const current = middlewares[index];
        return await current(url, init, (nextUrl, nextInit) => handler(index + 1, nextUrl, nextInit));
    };
    return (url, init) => handler(0, url, init);
}
async function fetchUrl(request) {
    const { url, init } = getFetchParams(request);
    const response = await request.fetch(url, init);
    return response;
}
function createFetch(fetch) {
    const fun = async (payload, init) => {
        try {
            return await fetch(payload, init);
        }
        catch (err) {
            if (err instanceof ApiError) {
                throw new fun.Error(err);
            }
            throw err;
        }
    };
    fun.Error = class extends ApiError {
        constructor(error) {
            super(error);
            Object.setPrototypeOf(this, new.target.prototype);
        }
        getActualType() {
            return {
                status: this.status,
                data: this.data,
            };
        }
    };
    return fun;
}
function fetcher() {
    let baseUrl = '';
    let defaultInit = {};
    const middlewares = [];
    const fetch = wrapMiddlewares(middlewares, fetchJson);
    return {
        configure: (config) => {
            baseUrl = config.baseUrl || '';
            defaultInit = config.init || {};
            middlewares.splice(0);
            middlewares.push(...(config.use || []));
        },
        use: (mw) => middlewares.push(mw),
        path: (path) => ({
            method: (method) => ({
                create: ((queryParams) => createFetch((payload, init) => fetchUrl({
                    baseUrl: baseUrl || '',
                    path: path,
                    method: method,
                    queryParams: Object.keys(queryParams || {}),
                    payload,
                    init: mergeRequestInit(defaultInit, init),
                    fetch,
                }))),
            }),
        }),
    };
}
const Fetcher = {
    for: () => fetcher(),
};

const MAX_CONTENT = 200;
class CustomError extends Error {
    constructor(message) {
        super(message);
        this.name = this.constructor.name;
        Object.setPrototypeOf(this, new.target.prototype);
    }
}
class QdrantClientUnexpectedResponseError extends CustomError {
    static forResponse(response) {
        const statusCodeStr = `${response.status}`;
        const reasonPhraseStr = !response.statusText ? '(Unrecognized Status Code)' : `(${response.statusText})`;
        const statusStr = `${statusCodeStr} ${reasonPhraseStr}`.trim();
        const dataStr = response.data ? JSON.stringify(response.data, null, 2) : null;
        let shortContent = '';
        if (dataStr) {
            shortContent = dataStr.length <= MAX_CONTENT ? dataStr : dataStr.slice(0, -4) + ' ...';
        }
        const rawContentStr = `Raw response content:\n${shortContent}`;
        return new QdrantClientUnexpectedResponseError(`Unexpected Response: ${statusStr}\n${rawContentStr}`);
    }
}
class QdrantClientConfigError extends CustomError {
}
class QdrantClientTimeoutError extends CustomError {
}
class QdrantClientResourceExhaustedError extends CustomError {
    constructor(message, retryAfter) {
        super(message);
        const retryAfterNumber = Number(retryAfter);
        if (isNaN(retryAfterNumber)) {
            throw new CustomError(`Invalid retryAfter value: ${retryAfter}`);
        }
        this.retry_after = retryAfterNumber;
        Object.setPrototypeOf(this, new.target.prototype);
    }
}

// AUTOMATICALLY GENERATED FILE. DO NOT EDIT!
function createClientApi(client) {
    return {
        /** Create shard key */
        createShardKey: client
            .path('/collections/{collection_name}/shards')
            .method('put')
            .create({
            timeout: true,
        }),
        /** Delete shard key */
        deleteShardKey: client
            .path('/collections/{collection_name}/shards/delete')
            .method('post')
            .create({
            timeout: true,
        }),
        /**
             * Returns information about the running Qdrant instance
             * @description Returns information about the running Qdrant instance like version and commit id
             */
        root: client
            .path('/')
            .method('get')
            .create(),
        /**
             * Collect telemetry data
             * @description Collect telemetry data including app info, system info, collections info, cluster info, configs and statistics
             */
        telemetry: client
            .path('/telemetry')
            .method('get')
            .create(),
        /**
             * Collect Prometheus metrics data
             * @description Collect metrics data including app info, collections info, cluster info and statistics
             */
        metrics: client
            .path('/metrics')
            .method('get')
            .create(),
        /**
             * Get lock options
             * @deprecated
             * @description Get lock options. If write is locked, all write operations and collection creation are forbidden
             */
        getLocks: client
            .path('/locks')
            .method('get')
            .create(),
        /**
             * Set lock options
             * @deprecated
             * @description Set lock options. If write is locked, all write operations and collection creation are forbidden. Returns previous lock options
             */
        postLocks: client
            .path('/locks')
            .method('post')
            .create(),
        /**
             * Kubernetes healthz endpoint
             * @description An endpoint for health checking used in Kubernetes.
             */
        healthz: client
            .path('/healthz')
            .method('get')
            .create(),
        /**
             * Kubernetes livez endpoint
             * @description An endpoint for health checking used in Kubernetes.
             */
        livez: client
            .path('/livez')
            .method('get')
            .create(),
        /**
             * Kubernetes readyz endpoint
             * @description An endpoint for health checking used in Kubernetes.
             */
        readyz: client
            .path('/readyz')
            .method('get')
            .create(),
        /**
             * Get issues
             * @description Get a report of performance issues and configuration suggestions
             */
        getIssues: client
            .path('/issues')
            .method('get')
            .create(),
        /**
             * Clear issues
             * @description Removes all issues reported so far
             */
        clearIssues: client
            .path('/issues')
            .method('delete')
            .create(),
        /**
             * Get cluster status info
             * @description Get information about the current state and composition of the cluster
             */
        clusterStatus: client
            .path('/cluster')
            .method('get')
            .create(),
        /** Tries to recover current peer Raft state. */
        recoverCurrentPeer: client
            .path('/cluster/recover')
            .method('post')
            .create(),
        /**
             * Remove peer from the cluster
             * @description Tries to remove peer from the cluster. Will return an error if peer has shards on it.
             */
        removePeer: client
            .path('/cluster/peer/{peer_id}')
            .method('delete')
            .create({
            force: true,
        }),
        /**
             * List collections
             * @description Get list name of all existing collections
             */
        getCollections: client
            .path('/collections')
            .method('get')
            .create(),
        /**
             * Collection info
             * @description Get detailed information about specified existing collection
             */
        getCollection: client
            .path('/collections/{collection_name}')
            .method('get')
            .create(),
        /**
             * Create collection
             * @description Create new collection with given parameters
             */
        createCollection: client
            .path('/collections/{collection_name}')
            .method('put')
            .create({
            timeout: true,
        }),
        /**
             * Delete collection
             * @description Drop collection and all associated data
             */
        deleteCollection: client
            .path('/collections/{collection_name}')
            .method('delete')
            .create({
            timeout: true,
        }),
        /**
             * Update collection parameters
             * @description Update parameters of the existing collection
             */
        updateCollection: client
            .path('/collections/{collection_name}')
            .method('patch')
            .create({
            timeout: true,
        }),
        /** Update aliases of the collections */
        updateAliases: client
            .path('/collections/aliases')
            .method('post')
            .create({
            timeout: true,
        }),
        /**
             * Create index for field in collection
             * @description Create index for field in collection
             */
        createFieldIndex: client
            .path('/collections/{collection_name}/index')
            .method('put')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Check the existence of a collection
             * @description Returns "true" if the given collection name exists, and "false" otherwise
             */
        collectionExists: client
            .path('/collections/{collection_name}/exists')
            .method('get')
            .create(),
        /**
             * Delete index for field in collection
             * @description Delete field index for collection
             */
        deleteFieldIndex: client
            .path('/collections/{collection_name}/index/{field_name}')
            .method('delete')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Collection cluster info
             * @description Get cluster information for a collection
             */
        collectionClusterInfo: client
            .path('/collections/{collection_name}/cluster')
            .method('get')
            .create(),
        /** Update collection cluster setup */
        updateCollectionCluster: client
            .path('/collections/{collection_name}/cluster')
            .method('post')
            .create({
            timeout: true,
        }),
        /**
             * List aliases for collection
             * @description Get list of all aliases for a collection
             */
        getCollectionAliases: client
            .path('/collections/{collection_name}/aliases')
            .method('get')
            .create(),
        /**
             * List collections aliases
             * @description Get list of all existing collections aliases
             */
        getCollectionsAliases: client
            .path('/aliases')
            .method('get')
            .create(),
        /**
             * Recover from an uploaded snapshot
             * @description Recover local collection data from an uploaded snapshot. This will overwrite any data, stored on this node, for the collection. If collection does not exist - it will be created.
             */
        recoverFromUploadedSnapshot: client
            .path('/collections/{collection_name}/snapshots/upload')
            .method('post')
            .create({
            wait: true,
            priority: true,
            checksum: true,
        }),
        /**
             * Recover from a snapshot
             * @description Recover local collection data from a snapshot. This will overwrite any data, stored on this node, for the collection. If collection does not exist - it will be created.
             */
        recoverFromSnapshot: client
            .path('/collections/{collection_name}/snapshots/recover')
            .method('put')
            .create({
            wait: true,
        }),
        /**
             * List collection snapshots
             * @description Get list of snapshots for a collection
             */
        listSnapshots: client
            .path('/collections/{collection_name}/snapshots')
            .method('get')
            .create(),
        /**
             * Create collection snapshot
             * @description Create new snapshot for a collection
             */
        createSnapshot: client
            .path('/collections/{collection_name}/snapshots')
            .method('post')
            .create({
            wait: true,
        }),
        /**
             * Download collection snapshot
             * @description Download specified snapshot from a collection as a file
             */
        getSnapshot: client
            .path('/collections/{collection_name}/snapshots/{snapshot_name}')
            .method('get')
            .create(),
        /**
             * Delete collection snapshot
             * @description Delete snapshot for a collection
             */
        deleteSnapshot: client
            .path('/collections/{collection_name}/snapshots/{snapshot_name}')
            .method('delete')
            .create({
            wait: true,
        }),
        /**
             * List of storage snapshots
             * @description Get list of snapshots of the whole storage
             */
        listFullSnapshots: client
            .path('/snapshots')
            .method('get')
            .create(),
        /**
             * Create storage snapshot
             * @description Create new snapshot of the whole storage
             */
        createFullSnapshot: client
            .path('/snapshots')
            .method('post')
            .create({
            wait: true,
        }),
        /**
             * Download storage snapshot
             * @description Download specified snapshot of the whole storage as a file
             */
        getFullSnapshot: client
            .path('/snapshots/{snapshot_name}')
            .method('get')
            .create(),
        /**
             * Delete storage snapshot
             * @description Delete snapshot of the whole storage
             */
        deleteFullSnapshot: client
            .path('/snapshots/{snapshot_name}')
            .method('delete')
            .create({
            wait: true,
        }),
        /**
             * Recover shard from an uploaded snapshot
             * @description Recover shard of a local collection from an uploaded snapshot. This will overwrite any data, stored on this node, for the collection shard.
             */
        recoverShardFromUploadedSnapshot: client
            .path('/collections/{collection_name}/shards/{shard_id}/snapshots/upload')
            .method('post')
            .create({
            wait: true,
            priority: true,
            checksum: true,
        }),
        /**
             * Recover from a snapshot
             * @description Recover shard of a local collection data from a snapshot. This will overwrite any data, stored in this shard, for the collection.
             */
        recoverShardFromSnapshot: client
            .path('/collections/{collection_name}/shards/{shard_id}/snapshots/recover')
            .method('put')
            .create({
            wait: true,
        }),
        /**
             * List shards snapshots for a collection
             * @description Get list of snapshots for a shard of a collection
             */
        listShardSnapshots: client
            .path('/collections/{collection_name}/shards/{shard_id}/snapshots')
            .method('get')
            .create(),
        /**
             * Create shard snapshot
             * @description Create new snapshot of a shard for a collection
             */
        createShardSnapshot: client
            .path('/collections/{collection_name}/shards/{shard_id}/snapshots')
            .method('post')
            .create({
            wait: true,
        }),
        /**
             * Download collection snapshot
             * @description Download specified snapshot of a shard from a collection as a file
             */
        getShardSnapshot: client
            .path('/collections/{collection_name}/shards/{shard_id}/snapshots/{snapshot_name}')
            .method('get')
            .create(),
        /**
             * Delete shard snapshot
             * @description Delete snapshot of a shard for a collection
             */
        deleteShardSnapshot: client
            .path('/collections/{collection_name}/shards/{shard_id}/snapshots/{snapshot_name}')
            .method('delete')
            .create({
            wait: true,
        }),
        /**
             * Get point
             * @description Retrieve full information of single point by id
             */
        getPoint: client
            .path('/collections/{collection_name}/points/{id}')
            .method('get')
            .create(),
        /**
             * Upsert points
             * @description Perform insert + updates on points. If point with given ID already exists - it will be overwritten.
             */
        upsertPoints: client
            .path('/collections/{collection_name}/points')
            .method('put')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Get points
             * @description Retrieve multiple points by specified IDs
             */
        getPoints: client
            .path('/collections/{collection_name}/points')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Delete points
             * @description Delete points
             */
        deletePoints: client
            .path('/collections/{collection_name}/points/delete')
            .method('post')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Update vectors
             * @description Update specified named vectors on points, keep unspecified vectors intact.
             */
        updateVectors: client
            .path('/collections/{collection_name}/points/vectors')
            .method('put')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Delete vectors
             * @description Delete named vectors from the given points.
             */
        deleteVectors: client
            .path('/collections/{collection_name}/points/vectors/delete')
            .method('post')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Overwrite payload
             * @description Replace full payload of points with new one
             */
        overwritePayload: client
            .path('/collections/{collection_name}/points/payload')
            .method('put')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Set payload
             * @description Set payload values for points
             */
        setPayload: client
            .path('/collections/{collection_name}/points/payload')
            .method('post')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Delete payload
             * @description Delete specified key payload for points
             */
        deletePayload: client
            .path('/collections/{collection_name}/points/payload/delete')
            .method('post')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Clear payload
             * @description Remove all payload for specified points
             */
        clearPayload: client
            .path('/collections/{collection_name}/points/payload/clear')
            .method('post')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Batch update points
             * @description Apply a series of update operations for points, vectors and payloads
             */
        batchUpdate: client
            .path('/collections/{collection_name}/points/batch')
            .method('post')
            .create({
            wait: true,
            ordering: true,
        }),
        /**
             * Scroll points
             * @description Scroll request - paginate over all points which matches given filtering condition
             */
        scrollPoints: client
            .path('/collections/{collection_name}/points/scroll')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Search points
             * @deprecated
             * @description Retrieve closest points based on vector similarity and given filtering conditions
             */
        searchPoints: client
            .path('/collections/{collection_name}/points/search')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Search batch points
             * @deprecated
             * @description Retrieve by batch the closest points based on vector similarity and given filtering conditions
             */
        searchBatchPoints: client
            .path('/collections/{collection_name}/points/search/batch')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Search point groups
             * @deprecated
             * @description Retrieve closest points based on vector similarity and given filtering conditions, grouped by a given payload field
             */
        searchPointGroups: client
            .path('/collections/{collection_name}/points/search/groups')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Recommend points
             * @deprecated
             * @description Look for the points which are closer to stored positive examples and at the same time further to negative examples.
             */
        recommendPoints: client
            .path('/collections/{collection_name}/points/recommend')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Recommend batch points
             * @deprecated
             * @description Look for the points which are closer to stored positive examples and at the same time further to negative examples.
             */
        recommendBatchPoints: client
            .path('/collections/{collection_name}/points/recommend/batch')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Recommend point groups
             * @deprecated
             * @description Look for the points which are closer to stored positive examples and at the same time further to negative examples, grouped by a given payload field.
             */
        recommendPointGroups: client
            .path('/collections/{collection_name}/points/recommend/groups')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Discover points
             * @deprecated
             * @description Use context and a target to find the most similar points to the target, constrained by the context.
             * When using only the context (without a target), a special search - called context search - is performed where pairs of points are used to generate a loss that guides the search towards the zone where most positive examples overlap. This means that the score minimizes the scenario of finding a point closer to a negative than to a positive part of a pair.
             * Since the score of a context relates to loss, the maximum score a point can get is 0.0, and it becomes normal that many points can have a score of 0.0.
             * When using target (with or without context), the score behaves a little different: The  integer part of the score represents the rank with respect to the context, while the decimal part of the score relates to the distance to the target. The context part of the score for  each pair is calculated +1 if the point is closer to a positive than to a negative part of a pair,  and -1 otherwise.
             */
        discoverPoints: client
            .path('/collections/{collection_name}/points/discover')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Discover batch points
             * @deprecated
             * @description Look for points based on target and/or positive and negative example pairs, in batch.
             */
        discoverBatchPoints: client
            .path('/collections/{collection_name}/points/discover/batch')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Count points
             * @description Count points which matches given filtering condition
             */
        countPoints: client
            .path('/collections/{collection_name}/points/count')
            .method('post')
            .create({
            timeout: true,
        }),
        /**
             * Facet a payload key with a given filter.
             * @description Count points that satisfy the given filter for each unique value of a payload key.
             */
        facet: client
            .path('/collections/{collection_name}/facet')
            .method('post')
            .create({
            timeout: true,
            consistency: true,
        }),
        /**
             * Query points
             * @description Universally query points. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
             */
        queryPoints: client
            .path('/collections/{collection_name}/points/query')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Query points in batch
             * @description Universally query points in batch. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
             */
        queryBatchPoints: client
            .path('/collections/{collection_name}/points/query/batch')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Query points, grouped by a given payload field
             * @description Universally query points, grouped by a given payload field
             */
        queryPointsGroups: client
            .path('/collections/{collection_name}/points/query/groups')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Search points matrix distance pairs
             * @description Compute distance matrix for sampled points with a pair based output format
             */
        searchMatrixPairs: client
            .path('/collections/{collection_name}/points/search/matrix/pairs')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
        /**
             * Search points matrix distance offsets
             * @description Compute distance matrix for sampled points with an offset based output format
             */
        searchMatrixOffsets: client
            .path('/collections/{collection_name}/points/search/matrix/offsets')
            .method('post')
            .create({
            consistency: true,
            timeout: true,
        }),
    };
}

function createApis(baseUrl, args) {
    const client = createClient(baseUrl, args);
    return createClientApi(client);
}
function createClient(baseUrl, { headers, timeout, connections }) {
    const use = [];
    if (Number.isFinite(timeout)) {
        use.push(async (url, init, next) => {
            const controller = new AbortController();
            const id = setTimeout(() => controller.abort(), timeout);
            try {
                return await next(url, Object.assign(init, { signal: controller.signal }));
            }
            catch (e) {
                if (e instanceof Error && e.name === 'AbortError') {
                    throw new QdrantClientTimeoutError(e.message);
                }
                throw e;
            }
            finally {
                clearTimeout(id);
            }
        });
    }
    use.push(async (url, init, next) => {
        let response;
        try {
            response = await next(url, init);
            if (response.status === 200 || response.status === 201) {
                return response;
            }
        }
        catch (error) {
            if (error instanceof ApiError && error.status === 429) {
                const retryAfterHeader = error.headers.get('retry-after')?.[0];
                if (retryAfterHeader) {
                    throw new QdrantClientResourceExhaustedError(error.message, retryAfterHeader);
                }
            }
            throw error;
        }
        throw QdrantClientUnexpectedResponseError.forResponse(response);
    });
    const client = Fetcher.for();
    // Configure client with 'undici' agent which is used in Node 18+
    client.configure({
        baseUrl,
        init: {
            headers,
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            dispatcher: undefined,
        },
        use,
    });
    return client;
}

const PACKAGE_VERSION = '1.15.1';
const ClientVersion = {
    /**
     * Parses a version string into a structured Version object.
     * @param version - The version string to parse (e.g., "1.2.3").
     * @returns A Version object.
     * @throws If the version format is invalid.
     */
    parseVersion(version) {
        if (!version) {
            throw new Error('Version is null');
        }
        let major = undefined;
        let minor = undefined;
        [major, minor] = version.split('.', 2);
        major = parseInt(major, 10);
        minor = parseInt(minor, 10);
        if (isNaN(major) || isNaN(minor)) {
            throw new Error(`Unable to parse version, expected format: x.y[.z], found: ${version}`);
        }
        return {
            major,
            minor,
        };
    },
    /**
     * Checks if the client version is compatible with the server version.
     * @param clientVersion - The client version string.
     * @param serverVersion - The server version string.
     * @returns True if compatible, otherwise false.
     */
    isCompatible(clientVersion, serverVersion) {
        if (!clientVersion || !serverVersion) {
            console.debug(`Unable to compare versions with null values. Client: ${clientVersion}, Server: ${serverVersion}`);
            return false;
        }
        if (clientVersion === serverVersion)
            return true;
        try {
            const client = ClientVersion.parseVersion(clientVersion);
            const server = ClientVersion.parseVersion(serverVersion);
            return client.major === server.major && Math.abs(client.minor - server.minor) <= 1;
        }
        catch (error) {
            console.debug(`Unable to compare versions: ${error}`);
            return false;
        }
    },
};

/* eslint-disable @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call */
class QdrantClient {
    constructor({ url, host, apiKey, https, prefix, port = 6333, timeout = 300000, checkCompatibility = true, ...args } = {}) {
        this._https = https ?? typeof apiKey === 'string';
        this._scheme = this._https ? 'https' : 'http';
        this._prefix = prefix ?? '';
        if (this._prefix.length > 0 && !this._prefix.startsWith('/')) {
            this._prefix = `/${this._prefix}`;
        }
        if (url && host) {
            throw new QdrantClientConfigError(`Only one of \`url\`, \`host\` params can be set. Url is ${url}, host is ${host}`);
        }
        if (host && (host.startsWith('http://') || host.startsWith('https://') || /:\d+$/.test(host))) {
            throw new QdrantClientConfigError('The `host` param is not expected to contain neither protocol (http:// or https://) nor port (:6333).\n' +
                'Try to use the `url` parameter instead.');
        }
        else if (url) {
            if (!(url.startsWith('http://') || url.startsWith('https://'))) {
                throw new QdrantClientConfigError('The `url` param expected to contain a valid URL starting with a protocol (http:// or https://).');
            }
            const parsedUrl = new URL(url);
            this._host = parsedUrl.hostname;
            this._port = parsedUrl.port ? Number(parsedUrl.port) : port;
            this._scheme = parsedUrl.protocol.replace(':', '');
            if (this._prefix.length > 0 && parsedUrl.pathname !== '/') {
                throw new QdrantClientConfigError('Prefix can be set either in `url` or in `prefix`.\n' +
                    `url is ${url}, prefix is ${parsedUrl.pathname}`);
            }
        }
        else {
            this._port = port;
            this._host = host ?? '127.0.0.1';
        }
        const headers = new Headers([['user-agent', 'qdrant-js/' + String(PACKAGE_VERSION)]]);
        const metadata = args.headers ?? {};
        Object.keys(metadata).forEach((field) => {
            if (metadata[field]) {
                headers.set(field, String(metadata[field]));
            }
        });
        if (typeof apiKey === 'string') {
            if (this._scheme === 'http') {
                console.warn('Api key is used with unsecure connection.');
            }
            headers.set('api-key', apiKey);
        }
        const address = this._port ? `${this._host}:${this._port}` : this._host;
        this._restUri = `${this._scheme}://${address}${this._prefix}`;
        const connections = args.maxConnections;
        const restArgs = { headers, timeout, connections };
        this._openApiClient = createApis(this._restUri, restArgs);
        if (checkCompatibility) {
            this._openApiClient
                .root({})
                .then((response) => {
                const serverVersion = response.data.version;
                if (!ClientVersion.isCompatible(PACKAGE_VERSION, serverVersion)) {
                    console.warn(`Client version ${PACKAGE_VERSION} is incompatible with server version ${serverVersion}. Major versions should match and minor version difference must not exceed 1. Set checkCompatibility=false to skip version check.`);
                }
            })
                .catch(() => {
                console.warn(`Failed to obtain server version. Unable to check client-server compatibility. Set checkCompatibility=false to skip version check.`);
            });
        }
    }
    /**
     * API getter
     *
     * @returns An instance of an API, generated from OpenAPI schema.
     */
    api() {
        return this._openApiClient;
    }
    /**
     * Search for points in multiple collections
     *
     * @param collectionName Name of the collection
     * @param {object} args -
     *     - searches: List of search requests
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     * @returns List of search responses
     */
    async searchBatch(collection_name, { searches, consistency, timeout, }) {
        const response = await this._openApiClient.searchBatchPoints({
            collection_name,
            consistency,
            timeout,
            searches,
        });
        return maybe(response.data.result).orThrow('Search batch returned empty');
    }
    /**
     * Search for closest vectors in collection taking into account filtering conditions
     *
     * @param collection_name Collection to search in
     * @param {object} args -
     *      - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *      - vector:
     *          Search for vectors closest to this.
     *          Can be either a vector itself, or a named vector, or a tuple of vector name and vector itself
     *      - filter:
     *          - Exclude vectors which doesn't fit given conditions.
     *          - If `None` - search among all vectors
     *      - params: Additional search params
     *      - limit: How many results return
     *      - offset:
     *          Offset of the first result to return.
     *          May be used to paginate results.
     *          Note: large offset values may cause performance issues.
     *      - with_payload:
     *          - Specify which stored payload should be attached to the result.
     *          - If `True` - attach all payload
     *          - If `False` - do not attach any payload
     *          - If List of string - include only specified fields
     *          - If `PayloadSelector` - use explicit rules
     *      - with_vector:
     *          - If `True` - Attach stored vector to the search result.
     *          - If `False` - Do not attach vector.
     *          - If List of string - include only specified fields
     *          - Default: `False`
     *      - score_threshold:
     *          Define a minimal score threshold for the result.
     *          If defined, less similar results will not be returned.
     *          Score of the returned result might be higher or smaller than the threshold depending
     *          on the Distance function used.
     *          E.g. for cosine similarity only higher scores will be returned.
     *      - consistency:
     *          Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *          Values:
     *              - int - number of replicas to query, values should present in all queried replicas
     *              - 'majority' - query all replicas, but return values present in the majority of replicas
     *              - 'quorum' - query the majority of replicas, return values present in all of them
     *              - 'all' - query all replicas, and return values present in all replicas
     *      - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     * @example
     *     // Search with filter
     *     client.search(
     *         "test_collection",
     *         {
     *             vector: [1.0, 0.1, 0.2, 0.7],
     *             filter: {
     *                 must: [
     *                     {
     *                         key: 'color',
     *                         range: {
     *                             color: 'red'
     *                         }
     *                     }
     *                 ]
     *             )
     *         }
     *     )
     * @returns List of found close points with similarity scores.
     */
    async search(collection_name, { shard_key, vector, limit = 10, offset = 0, filter, params, with_payload = true, with_vector = false, score_threshold, consistency, timeout, }) {
        const response = await this._openApiClient.searchPoints({
            collection_name,
            consistency,
            timeout,
            shard_key,
            vector,
            limit,
            offset,
            filter,
            params,
            with_payload,
            with_vector,
            score_threshold,
        });
        return maybe(response.data.result).orThrow('Search returned empty');
    }
    /**
     * Perform multiple recommend requests in batch mode
     * @param collection_name Name of the collection
     * @param {object} args
     *     - searches: List of recommend requests
     *     - consistency:
     *         Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             - number - number of replicas to query, values should present in all queried replicas
     *             - 'majority' - query all replicas, but return values present in the majority of replicas
     *             - 'quorum' - query the majority of replicas, return values present in all of them
     *             - 'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     * @returns List of recommend responses
     */
    async recommendBatch(collection_name, { searches, consistency, timeout, }) {
        const response = await this._openApiClient.recommendBatchPoints({
            collection_name,
            searches,
            consistency,
            timeout,
        });
        return maybe(response.data.result).orElse([]);
    }
    /**
     * @alias recommendBatch
     */
    async recommend_batch(collection_name, { searches, consistency, timeout, }) {
        const response = await this._openApiClient.recommendBatchPoints({
            collection_name,
            searches,
            consistency,
            timeout,
        });
        return maybe(response.data.result).orElse([]);
    }
    /**
     * Recommendation request. Provides positive and negative examples of the vectors,
     * which can be ids of points that are already stored in the collection, raw vectors, or even ids and vectors combined.
     * Service should look for the points which are closer to positive examples and at the same time further to negative examples.
     * The concrete way of how to compare negative and positive distances is up to the `strategy` chosen.
     * @param collection_name Collection to search in
     * @param {object} args
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - positive:
     *         List of stored point IDs, which should be used as reference for similarity search.
     *         If there is only one ID provided - this request is equivalent to the regular search with vector of that point.
     *         If there are more than one IDs, Qdrant will attempt to search for similar to all of them.
     *         Recommendation for multiple vectors is experimental. Its behaviour may change in the future.
     *     - negative:
     *         List of stored point IDs, which should be dissimilar to the search result.
     *         Negative examples is an experimental functionality. Its behaviour may change in the future.
     *     - strategy:
     *         How to use positive and negative examples to find the results.
     *     - query_filter:
     *         - Exclude vectors which doesn't fit given conditions.
     *         - If `None` - search among all vectors
     *     - search_params: Additional search params
     *     - limit: How many results return
     *         - Default: `10`
     *     - offset:
     *         Offset of the first result to return.
     *         May be used to paginate results.
     *         Note: large offset values may cause performance issues.
     *         - Default: `0`
     *     - with_payload:
     *         - Specify which stored payload should be attached to the result.
     *         - If `True` - attach all payload
     *         - If `False` - do not attach any payload
     *         - If List of string - include only specified fields
     *         - If `PayloadSelector` - use explicit rules
     *         - Default: `true`
     *     - with_vector:
     *         - If `True` - Attach stored vector to the search result.
     *         - If `False` - Do not attach vector.
     *         - If List of string - include only specified fields
     *         - Default: `false`
     *     - score_threshold:
     *         Define a minimal score threshold for the result.
     *         If defined, less similar results will not be returned.
     *         Score of the returned result might be higher or smaller than the threshold depending
     *         on the Distance function used.
     *         E.g. for cosine similarity only higher scores will be returned.
     *     - using:
     *         Name of the vectors to use for recommendations.
     *         If `None` - use default vectors.
     *     - lookupFrom:
     *         Defines a location (collection and vector field name), used to lookup vectors for recommendations.
     *         If `None` - use current collection will be used.
     *     - consistency:
     *         Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *         - int - number of replicas to query, values should present in all queried replicas
     *         - 'majority' - query all replicas, but return values present in the majority of replicas
     *         - 'quorum' - query the majority of replicas, return values present in all of them
     *         - 'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     * @returns List of recommended points with similarity scores.
     */
    async recommend(collection_name, { shard_key, positive, negative, strategy, filter, params, limit = 10, offset = 0, with_payload = true, with_vector = false, score_threshold, using, lookup_from, consistency, timeout, }) {
        const response = await this._openApiClient.recommendPoints({
            collection_name,
            limit,
            shard_key,
            positive,
            negative,
            strategy,
            filter,
            params,
            offset,
            with_payload,
            with_vector,
            score_threshold,
            using,
            lookup_from,
            consistency,
            timeout,
        });
        return maybe(response.data.result).orThrow('Recommend points API returned empty');
    }
    /**
     * Scroll over all (matching) points in the collection.
     * @param collection_name Name of the collection
     * @param {object} args
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - filter: If provided - only returns points matching filtering conditions
     *     - limit: How many points to return
     *     - offset: If provided - skip points with ids less than given `offset`
     *     - with_payload:
     *         - Specify which stored payload should be attached to the result.
     *         - If `True` - attach all payload
     *         - If `False` - do not attach any payload
     *         - If List of string - include only specified fields
     *         - If `PayloadSelector` - use explicit rules
     *         - Default: `true`
     *     - with_vector:
     *         - If `True` - Attach stored vector to the search result.
     *         - If `False` - Do not attach vector.
     *         - If List of string - include only specified fields
     *         - Default: `false`
     *     - consistency:
     *         Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *         - int - number of replicas to query, values should present in all queried replicas
     *         - 'majority' - query all replicas, but return values present in the majority of replicas
     *         - 'quorum' - query the majority of replicas, return values present in all of them
     *         - 'all' - query all replicas, and return values present in all replicas
     *     - order_by:
     *         Order the records by a payload field.
     * @returns
     *     A pair of (List of points) and (optional offset for the next scroll request).
     *     If next page offset is `None` - there is no more points in the collection to scroll.
     */
    async scroll(collection_name, { shard_key, filter, consistency, timeout, limit = 10, offset, with_payload = true, with_vector = false, order_by, } = {}) {
        const response = await this._openApiClient.scrollPoints({
            collection_name,
            shard_key,
            limit,
            offset,
            filter,
            with_payload,
            with_vector,
            order_by,
            consistency,
            timeout,
        });
        return maybe(response.data.result).orThrow('Scroll points API returned empty');
    }
    /**
     * Count points in the collection.
     * Count points in the collection matching the given filter.
     * @param collection_name
     * @param {object} args
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - filter: filtering conditions
     *     - exact:
     *         If `True` - provide the exact count of points matching the filter.
     *         If `False` - provide the approximate count of points matching the filter. Works faster.
     *         Default: `true`
     * @returns Amount of points in the collection matching the filter.
     */
    async count(collection_name, { shard_key, filter, exact = true, timeout } = {}) {
        const response = await this._openApiClient.countPoints({
            collection_name,
            shard_key,
            filter,
            exact,
            timeout,
        });
        return maybe(response.data.result).orThrow('Count points returned empty');
    }
    /**
     * Get cluster information for a collection.
     * @param collection_name
     * @returns Operation result
     */
    async collectionClusterInfo(collection_name) {
        const response = await this._openApiClient.collectionClusterInfo({ collection_name });
        return maybe(response.data.result).orThrow('Collection cluster info returned empty');
    }
    /**
     * Update collection cluster setup
     * @param collection_name Name of the collection
     * @param {object} args
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - operation: Cluster operation to perform. Can be one of:
     *         - move_shard: Move a shard from one peer to another
     *         - replicate_shard: Replicate a shard to another peer
     *         - abort_transfer: Abort an ongoing shard transfer
     *         - drop_replica: Drop a replica from a peer
     *         - create_sharding_key: Create a new sharding key
     *         - drop_sharding_key: Drop an existing sharding key
     *         - restart_transfer: Restart a failed shard transfer
     *         - start_resharding: Start resharding operation
     *         - abort_resharding: Abort an ongoing resharding operation
     * @returns Operation result
     */
    async updateCollectionCluster(collection_name, { timeout, ...operation }) {
        const response = await this._openApiClient.updateCollectionCluster({
            collection_name,
            timeout,
            ...operation,
        });
        return maybe(response.data.result).orThrow('Update collection cluster returned empty');
    }
    /**
     * Update vectors
     * @param collection_name
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *         - Default: `true`
     *     - ordering: Define strategy for ordering of the points. Possible values:
     *          - 'weak'   - write operations may be reordered, works faster, default
     *          - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *          - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     *     - points: Points with named vectors
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     * @returns Operation result
     */
    async updateVectors(collection_name, { wait = true, ordering, points, shard_key, }) {
        const response = await this._openApiClient.updateVectors({
            collection_name,
            wait,
            ordering,
            points,
            shard_key,
        });
        return maybe(response.data.result).orThrow('Update vectors returned empty');
    }
    /**
     * Delete vectors
     * @param collection_name
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *         - Default: `true`
     *     - ordering: Define strategy for ordering of the points. Possible values:
     *          - 'weak'   - write operations may be reordered, works faster, default
     *          - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *          - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     *     - points: Deletes values from each point in this list
     *     - filter: Deletes values from points that satisfy this filter condition
     *     - vector: Vector names
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     * @returns Operation result
     */
    async deleteVectors(collection_name, { wait = true, ordering, points, filter, vector, shard_key, }) {
        const response = await this._openApiClient.deleteVectors({
            collection_name,
            wait,
            ordering,
            points,
            filter,
            vector,
            shard_key,
        });
        return maybe(response.data.result).orThrow('Delete vectors returned empty');
    }
    /**
     * Search point groups
     * @param collection_name
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - vector: query search vector
     *     - filter: Look only for points which satisfies this conditions
     *     - params: Additional search params
     *     - with_payload: Select which payload to return with the response
     *     - with_vector: Whether to return the point vector with the result?
     *     - score_threshold: Define a minimal score threshold for the result. If defined, less similar results will not be returned. Score of the returned result might be higher or smaller than the threshold depending on the Distance function used. E.g. for cosine similarity only higher scores will be returned.
     *     - group_by: Payload field to group by, must be a string or number field. If the field contains more than 1 value, all values will be used for grouping. One point can be in multiple groups.
     *     - group_size: Maximum amount of points to return per group
     *     - limit: Maximum amount of groups to return
     * @returns Operation result
     */
    async searchPointGroups(collection_name, { consistency, timeout, shard_key, vector, filter, params, with_payload = null, with_vector = null, score_threshold, group_by, group_size, limit, }) {
        const response = await this._openApiClient.searchPointGroups({
            collection_name,
            consistency,
            timeout,
            shard_key,
            vector,
            filter,
            params,
            with_payload,
            with_vector,
            score_threshold,
            group_by,
            group_size,
            limit,
        });
        return maybe(response.data.result).orThrow('Search point groups returned empty');
    }
    /**
     * Recommend point groups
     * @param collection_name
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - positive: Look for vectors closest to those
     *     - negative: Try to avoid vectors like this
     *     - strategy: How to use positive and negative examples to find the results
     *     - filter: Look only for points which satisfies this conditions
     *     - params: Additional search params
     *     - with_payload: Select which payload to return with the response
     *     - with_vector: Whether to return the point vector with the result?
     *     - score_threshold: Define a minimal score threshold for the result. If defined, less similar results will not be returned. Score of the returned result might be higher or smaller than the threshold depending on the Distance function used. E.g. for cosine similarity only higher scores will be returned.
     *     - using: Define which vector to use for recommendation, if not specified - try to use default vector
     *     - lookup_from: The location used to lookup vectors. If not specified - use current collection. Note: the other collection should have the same vector size as the current collection
     *     - group_by: Payload field to group by, must be a string or number field. If the field contains more than 1 value, all values will be used for grouping. One point can be in multiple groups.
     *     - group_size: Maximum amount of points to return per group
     *     - limit: Maximum amount of groups to return
     * @returns Operation result
     */
    async recommendPointGroups(collection_name, { consistency, timeout, shard_key, positive, strategy, negative = [], filter, params, with_payload = null, with_vector = null, score_threshold, using = null, lookup_from = null, group_by, group_size, limit, }) {
        const response = await this._openApiClient.recommendPointGroups({
            collection_name,
            consistency,
            timeout,
            shard_key,
            positive,
            negative,
            strategy,
            filter,
            params,
            with_payload,
            with_vector,
            score_threshold,
            using,
            lookup_from,
            group_by,
            group_size,
            limit,
        });
        return maybe(response.data.result).orThrow('Recommend point groups API returned empty');
    }
    /**
     * Update or insert a new point into the collection.
     * @param collection_name
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *         - Default: `true`
     *     - ordering: Define strategy for ordering of the points. Possible values:
     *          - 'weak'   - write operations may be reordered, works faster, default
     *          - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *          - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     *     - points: Batch or list of points to insert
     * @returns Operation result
     */
    async upsert(collection_name, { wait = true, ordering, ...points_or_batch }) {
        const response = await this._openApiClient.upsertPoints({
            collection_name,
            wait,
            ordering,
            ...points_or_batch,
        });
        return maybe(response.data.result).orThrow('Upsert returned empty');
    }
    /**
     * Retrieve stored points by IDs
     * @param collection_name
     * @param {object} args
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - ids: list of IDs to lookup
     *     - with_payload:
     *         - Specify which stored payload should be attached to the result.
     *         - If `True` - attach all payload
     *         - If `False` - do not attach any payload
     *         - If List of string - include only specified fields
     *         - If `PayloadSelector` - use explicit rules
     *         - Default: `true`
     *     - with_vector:
     *         - If `True` - Attach stored vector to the search result.
     *         - If `False` - Do not attach vector.
     *         - If List of string - Attach only specified vectors.
     *         - Default: `false`
     *     - consistency:
     *         Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *             Values:
     *                 - number - number of replicas to query, values should present in all queried replicas
     *                 - 'majority' - query all replicas, but return values present in the majority of replicas
     *                 - 'quorum' - query the majority of replicas, return values present in all of them
     *                 - 'all' - query all replicas, and return values present in all replicas
     * @returns List of points
     */
    async retrieve(collection_name, { shard_key, ids, with_payload = true, with_vector, consistency, timeout, }) {
        const response = await this._openApiClient.getPoints({
            collection_name,
            shard_key,
            ids,
            with_payload,
            with_vector,
            consistency,
            timeout,
        });
        return maybe(response.data.result).orThrow('Retrieve API returned empty');
    }
    /**
     * Deletes selected points from collection
     * @param collection_name Name of the collection
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *      - ordering: Define strategy for ordering of the points. Possible values:
     *          - 'weak'   - write operations may be reordered, works faster, default
     *          - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *          - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     *     - points_selector: List of affected points, filter or points selector.
     *         Example:
     *             - `points: [
     *                   1, 2, 3, "cd3b53f0-11a7-449f-bc50-d06310e7ed90"
     *               ]`
     *             - `filter: {
     *                    must: [
     *                        {
     *                            key: 'rand_number',
     *                            range: {
     *                                gte: 0.7
     *                            }
     *                        }
     *                    ]
     *                }`
     * @returns Operation result
     */
    async delete(collection_name, { wait, ordering, ...points_selector }) {
        const response = await this._openApiClient.deletePoints({
            collection_name,
            wait,
            ordering,
            ...points_selector,
        });
        return maybe(response.data.result).orThrow('Delete points returned empty');
    }
    /**
     * Sets payload values for specified points.
     * @param collection_name Name of the collection
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *      - ordering: Define strategy for ordering of the points. Possible values:
     *          - 'weak'   - write operations may be reordered, works faster, default
     *          - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *          - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     *     - payload: Key-value pairs of payload to assign
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - key: Assigns payload to each point that satisfy this path of property
     *     - points|filter: List of affected points, filter or points selector.
     *         Example:
     *             - `points: [
     *                   1, 2, 3, "cd3b53f0-11a7-449f-bc50-d06310e7ed90"
     *               ]`
     *             - `filter: {
     *                    must: [
     *                        {
     *                            key: 'rand_number',
     *                            range: {
     *                                gte: 0.7
     *                            }
     *                        }
     *                    ]
     *                }`
     * @returns Operation result
     */
    async setPayload(collection_name, { payload, points, filter, shard_key, key, ordering, wait = true, }) {
        const response = await this._openApiClient.setPayload({
            collection_name,
            payload,
            points,
            filter,
            shard_key,
            key,
            wait,
            ordering,
        });
        return maybe(response.data.result).orThrow('Set payload returned empty');
    }
    /**
     * Overwrites payload of the specified points
     * After this operation is applied, only the specified payload will be present in the point.
     * The existing payload, even if the key is not specified in the payload, will be deleted.
     * @param collection_name Name of the collection
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *      - ordering: Define strategy for ordering of the points. Possible values:
     *          - 'weak'   - write operations may be reordered, works faster, default
     *          - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *          - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     *     - payload: Key-value pairs of payload to assign
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - key: Assigns payload to each point that satisfy this path of property
     *     - points|filter: List of affected points, filter or points selector.
     *         Example:
     *             - `points: [
     *                   1, 2, 3, "cd3b53f0-11a7-449f-bc50-d06310e7ed90"
     *               ]`
     *             - `filter: {
     *                    must: [
     *                        {
     *                            key: 'rand_number',
     *                            range: {
     *                                gte: 0.7
     *                            }
     *                        }
     *                    ]
     *                }`
     * @returns Operation result
     */
    async overwritePayload(collection_name, { ordering, payload, points, filter, shard_key, key, wait = true, }) {
        const response = await this._openApiClient.overwritePayload({
            collection_name,
            payload,
            points,
            filter,
            shard_key,
            key,
            wait,
            ordering,
        });
        return maybe(response.data.result).orThrow('Overwrite payload returned empty');
    }
    /**
     * Remove values from point's payload
     * @param collection_name Name of the collection
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *      - ordering: Define strategy for ordering of the points. Possible values:
     *          - 'weak'   - write operations may be reordered, works faster, default
     *          - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *          - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     *     - keys: List of payload keys to remove.
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - points|filter: List of affected points, filter or points selector.
     *         Example:
     *             - `points: [
     *                   1, 2, 3, "cd3b53f0-11a7-449f-bc50-d06310e7ed90"
     *               ]`
     *             - `filter: {
     *                    must: [
     *                        {
     *                            key: 'rand_number',
     *                            range: {
     *                                gte: 0.7
     *                            }
     *                        }
     *                    ]
     *                }`
     * @returns Operation result
     */
    async deletePayload(collection_name, { ordering, keys, points, filter, shard_key, wait = true, }) {
        const response = await this._openApiClient.deletePayload({
            collection_name,
            keys,
            points,
            filter,
            shard_key,
            wait,
            ordering,
        });
        return maybe(response.data.result).orThrow('Delete payload returned empty');
    }
    /**
     * Delete all payload for selected points
     * @param collection_name Name of the collection
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *      - ordering: Define strategy for ordering of the points. Possible values:
     *          - 'weak'   - write operations may be reordered, works faster, default
     *          - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *          - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     *     - points_selector: List of affected points, filter or points selector.
     *         Example:
     *             - `points: [
     *                   1, 2, 3, "cd3b53f0-11a7-449f-bc50-d06310e7ed90"
     *               ]`
     *             - `filter: {
     *                    must: [
     *                        {
     *                            key: 'rand_number',
     *                            range: {
     *                                gte: 0.7
     *                            }
     *                        }
     *                    ]
     *                }`
     * @returns Operation result
     */
    async clearPayload(collection_name, { ordering, wait = true, ...points_selector }) {
        const response = await this._openApiClient.clearPayload({
            collection_name,
            wait,
            ordering,
            ...points_selector,
        });
        return maybe(response.data.result).orThrow('Clear payload returned empty');
    }
    /**
     * Operation for performing changes of collection aliases.
     * Alias changes are atomic, meaning that no collection modifications can happen between alias operations.
     * @param {object} args
     *     - actions: List of operations to perform
     *     - timeout: Wait for operation commit timeout in seconds. If timeout is reached, request will return with service error.
     * @returns Operation result
     */
    async updateCollectionAliases({ actions, timeout }) {
        const response = await this._openApiClient.updateAliases({ actions, timeout });
        return maybe(response.data.result).orThrow('Update aliases returned empty');
    }
    /**
     * Get collection aliases
     * @param collection_name Name of the collection
     * @returns Collection aliases
     */
    async getCollectionAliases(collection_name) {
        const response = await this._openApiClient.getCollectionAliases({ collection_name });
        return maybe(response.data.result).orThrow('Get collection aliases returned empty');
    }
    /**
     * Get all aliases
     * @returns All aliases of all collections
     */
    async getAliases() {
        const response = await this._openApiClient.getCollectionsAliases({});
        return maybe(response.data.result).orThrow('Get aliases returned empty');
    }
    /**
     * Get list name of all existing collections
     * @returns List of the collections
     */
    async getCollections() {
        const response = await this._openApiClient.getCollections({});
        return maybe(response.data.result).orThrow('Get collections returned empty');
    }
    /**
     * Get detailed information about specified existing collection
     *
     * @param collection_name Name of the collection
     * @returns Detailed information about the collection
     */
    async getCollection(collection_name) {
        const response = await this._openApiClient.getCollection({ collection_name });
        return maybe(response.data.result).orThrow('Get collection returned empty');
    }
    /**
     * Update parameters of the collection
     *
     * @param collection_name Name of the collection
     * @param {object} args
     *     - optimizer_config: Override for optimizer configuration
     *     - collection_params: Override for collection parameters
     *     - timeout: Wait for operation commit timeout in seconds. If timeout is reached, request will return with service error.
     * @returns Operation result
     */
    async updateCollection(collection_name, args) {
        const response = await this._openApiClient.updateCollection({
            collection_name,
            ...args,
        });
        return maybe(response.data.result).orThrow('Update collection returned empty');
    }
    /**
     * Removes collection and all it's data
     * @param collection_name Name of the collection to delete
     * @param {object} args
     *     - timeout:
     *         Wait for operation commit timeout in seconds.
     *         If timeout is reached, request will return with service error.
     * @returns Operation result
     */
    async deleteCollection(collection_name, args) {
        const response = await this._openApiClient.deleteCollection({ collection_name, ...args });
        return maybe(response.data.result).orThrow('Delete collection returned empty');
    }
    /**
     * Create empty collection with given parameters
     * @returns Operation result
     * @param collectionName Name of the collection to recreate
     * @param {object} args
     *     - vectors_config:
     *         Configuration of the vector storage. Vector params contains size and distance for the vector storage.
     *         If dict is passed, service will create a vector storage for each key in the dict.
     *         If single VectorParams is passed, service will create a single anonymous vector storage.
     *     - shard_number: Number of shards in collection. Default is 1, minimum is 1.
     *     - sharding_method: Sharding method Default is Auto - points are distributed across all available shards Custom - points are distributed across shards according to shard key
     *     - replication_factor:
     *         Replication factor for collection. Default is 1, minimum is 1.
     *         Defines how many copies of each shard will be created.
     *         Have effect only in distributed mode.
     *     - write_consistency_factor:
     *         Write consistency factor for collection. Default is 1, minimum is 1.
     *         Defines how many replicas should apply the operation for us to consider it successful.
     *         Increasing this number will make the collection more resilient to inconsistencies, but will
     *         also make it fail if not enough replicas are available.
     *         Does not have any performance impact.
     *         Have effect only in distributed mode.
     *     - on_disk_payload:
     *         If true - point`s payload will not be stored in memory.
     *         It will be read from the disk every time it is requested.
     *         This setting saves RAM by (slightly) increasing the response time.
     *         Note: those payload values that are involved in filtering and are indexed - remain in RAM.
     *     - hnsw_config: Params for HNSW index
     *     - optimizers_config: Params for optimizer
     *     - wal_config: Params for Write-Ahead-Log
     *     - quantization_config: Params for quantization, if None - quantization will be disabled
     *     - init_from: Use data stored in another collection to initialize this collection
     *     - sparse_vectors: Sparse vector data config
     *     - strict_mode_config: Strict mode configuration
     *     - timeout:
     *         Wait for operation commit timeout in seconds.
     *         If timeout is reached, request will return with service error.
     */
    async createCollection(collection_name, { timeout, vectors, hnsw_config, init_from, on_disk_payload, optimizers_config, quantization_config, replication_factor, shard_number, sharding_method, wal_config, write_consistency_factor, sparse_vectors, strict_mode_config, }) {
        const response = await this._openApiClient.createCollection({
            collection_name,
            timeout,
            vectors,
            hnsw_config,
            init_from,
            on_disk_payload,
            optimizers_config,
            quantization_config,
            replication_factor,
            shard_number,
            sharding_method,
            wal_config,
            write_consistency_factor,
            sparse_vectors,
            strict_mode_config,
        });
        return maybe(response.data.result).orThrow('Create collection returned empty');
    }
    /**
     * Delete and create empty collection with given parameters
     * @returns Operation result
     * @param collectionName Name of the collection to recreate
     * @param {object} args
     *     - vectorsConfig:
     *         Configuration of the vector storage. Vector params contains size and distance for the vector storage.
     *         If dict is passed, service will create a vector storage for each key in the dict.
     *         If single VectorParams is passed, service will create a single anonymous vector storage.
     *     - shardNumber: Number of shards in collection. Default is 1, minimum is 1.
     *     - sharding_method: Sharding method Default is Auto - points are distributed across all available shards Custom - points are distributed across shards according to shard key
     *     - replicationFactor:
     *         Replication factor for collection. Default is 1, minimum is 1.
     *         Defines how many copies of each shard will be created.
     *         Have effect only in distributed mode.
     *     - writeConsistencyFactor:
     *         Write consistency factor for collection. Default is 1, minimum is 1.
     *         Defines how many replicas should apply the operation for us to consider it successful.
     *         Increasing this number will make the collection more resilient to inconsistencies, but will
     *         also make it fail if not enough replicas are available.
     *         Does not have any performance impact.
     *         Have effect only in distributed mode.
     *     - onDiskPayload:
     *         If true - point`s payload will not be stored in memory.
     *         It will be read from the disk every time it is requested.
     *         This setting saves RAM by (slightly) increasing the response time.
     *         Note: those payload values that are involved in filtering and are indexed - remain in RAM.
     *     - hnswConfig: Params for HNSW index
     *     - optimizersConfig: Params for optimizer
     *     - walConfig: Params for Write-Ahead-Log
     *     - quantizationConfig: Params for quantization, if None - quantization will be disabled
     *     - initFrom: Use data stored in another collection to initialize this collection
     *     - sparse_vectors: Sparse vector data config
     *     - strict_mode_config: Strict mode configuration
     *     - timeout:
     *         Wait for operation commit timeout in seconds.
     *         If timeout is reached, request will return with service error.
     */
    async recreateCollection(collection_name, { timeout, vectors, hnsw_config, init_from, on_disk_payload, optimizers_config, quantization_config, replication_factor, shard_number, sharding_method, wal_config, write_consistency_factor, sparse_vectors, strict_mode_config, }) {
        maybe(await this._openApiClient.deleteCollection({
            collection_name,
            timeout,
        }))
            .get('ok')
            .orThrow('Delete collection returned failed');
        const response = await this._openApiClient.createCollection({
            collection_name,
            timeout,
            vectors,
            hnsw_config,
            init_from,
            on_disk_payload,
            optimizers_config,
            quantization_config,
            replication_factor,
            shard_number,
            sharding_method,
            wal_config,
            write_consistency_factor,
            sparse_vectors,
            strict_mode_config,
        });
        return maybe(response).orThrow('Create collection returned empty');
    }
    /**
     * Creates index for a given payload field.
     * Indexed fields allow to perform filtered search operations faster.
     * @param collectionName Name of the collection
     * @param {object} args
     *     - fieldName: Name of the payload field.
     *     - fieldSchema: Type of data to index.
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *     - ordering:
     *         Define strategy for ordering of the points. Possible values:
     *         - 'weak'   - write operations may be reordered, works faster, default
     *         - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *         - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     * @returns Operation Result
     */
    async createPayloadIndex(collection_name, { wait, ordering, field_name, field_schema, }) {
        const response = await this._openApiClient.createFieldIndex({
            collection_name,
            field_name,
            field_schema,
            wait,
            ordering,
        });
        return maybe(response.data.result).orThrow('Create field index returned empty');
    }
    /**
     * Removes index for a given payload field.
     * @param collection_name Name of the collection
     * @param field_name Name of the payload field
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *     - ordering:
     *         Define strategy for ordering of the points. Possible values:
     *         - 'weak'   - write operations may be reordered, works faster, default
     *         - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *         - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     * @returns Operation Result
     */
    async deletePayloadIndex(collection_name, field_name, { wait = true, ordering } = {}) {
        const response = await this._openApiClient.deleteFieldIndex({
            collection_name,
            field_name,
            wait,
            ordering,
        });
        return maybe(response.data.result).orThrow('Delete field index returned empty');
    }
    /**
     * List all snapshots for a given collection
     * @param collection_name Name of the collection
     * @returns List of snapshots
     */
    async listSnapshots(collection_name) {
        const response = await this._openApiClient.listSnapshots({ collection_name });
        return maybe(response.data.result).orThrow('List snapshots API returned empty');
    }
    /**
     * Create snapshot for a given collection
     * @param collection_name Name of the collection
     * @returns Snapshot description
     */
    async createSnapshot(collection_name, args) {
        const response = await this._openApiClient.createSnapshot({ collection_name, ...args });
        return maybe(response.data.result).orNull();
    }
    /**
     * Delete snapshot for a given collection
     * @param collection_name Name of the collection
     * @param snapshot_name Snapshot id
     * @returns True if snapshot was deleted
     */
    async deleteSnapshot(collection_name, snapshot_name, args) {
        const response = await this._openApiClient.deleteSnapshot({ collection_name, snapshot_name, ...args });
        return maybe(response.data.result).orThrow('Delete snapshot API returned empty');
    }
    /**
     * List all snapshots for a whole storage
     * @returns List of snapshots
     */
    async listFullSnapshots() {
        const response = await this._openApiClient.listFullSnapshots({});
        return maybe(response.data.result).orThrow('List full snapshots API returned empty');
    }
    /**
     * Create snapshot for a whole storage
     * @returns Snapshot description
     */
    async createFullSnapshot(args) {
        const response = await this._openApiClient.createFullSnapshot(args ?? {});
        return maybe(response.data.result).orThrow('Create full snapshot API returned empty');
    }
    /**
     * Delete snapshot for a whole storage
     * @param snapshot_name Snapshot name
     * @returns True if the snapshot was deleted
     */
    async deleteFullSnapshot(snapshot_name, args) {
        const response = await this._openApiClient.deleteFullSnapshot({ snapshot_name, ...args });
        return maybe(response.data.result).orThrow('Delete full snapshot API returned empty');
    }
    /**
     * Recover collection from snapshot
     * @param collection_name Name of the collection
     * @param {object} args
     *     - location:
     *         URL of the snapshot.
     *         Example:
     *             - URL `http://localhost:8080/collections/my_collection/snapshots/my_snapshot`
     *             - Local path `file:///qdrant/snapshots/test_collection-2022-08-04-10-49-10.snapshot`
     *     - priority:
     *         Defines source of truth for snapshot recovery
     *             - `snapshot` means - prefer snapshot data over the current state
     *             - `replica` means - prefer existing data over the snapshot
     *         Default: `replica`
     *     - checksum:
     *         SHA256 checksum to verify snapshot integrity before recovery
     * @returns True if the snapshot was recovered
     */
    async recoverSnapshot(collection_name, { location, priority, checksum, api_key }) {
        const response = await this._openApiClient.recoverFromSnapshot({
            collection_name,
            location,
            priority,
            checksum,
            api_key,
        });
        return maybe(response.data.result).orThrow('Recover from snapshot API returned empty');
    }
    /**
     * Lock storage for writing
     */
    async lockStorage(reason) {
        const response = await this._openApiClient.postLocks({ write: true, error_message: reason });
        return maybe(response.data.result).orThrow('Lock storage returned empty');
    }
    /**
     * Unlock storage for writing.
     */
    async unlockStorage() {
        const response = await this._openApiClient.postLocks({ write: false });
        return maybe(response.data.result).orThrow('Post locks returned empty');
    }
    /**
     * Get current locks state.
     */
    async getLocks() {
        const response = await this._openApiClient.getLocks({});
        return maybe(response.data.result).orThrow('Get locks returned empty');
    }
    /**
     * Batch update points
     * Apply a series of update operations for points, vectors and payloads.
     * @param collection_name Name of the collection
     * @param {object} args
     *     - wait: Await for the results to be processed.
     *         - If `true`, result will be returned only when all changes are applied
     *         - If `false`, result will be returned immediately after the confirmation of receiving.
     *      - ordering: Define strategy for ordering of the points. Possible values:
     *          - 'weak'   - write operations may be reordered, works faster, default
     *          - 'medium' - write operations go through dynamically selected leader,
     *                      may be inconsistent for a short period of time in case of leader change
     *          - 'strong' - Write operations go through the permanent leader,
     *                      consistent, but may be unavailable if leader is down
     *      - operations: List of operations to perform
     * @returns Operation result
     */
    async batchUpdate(collection_name, { wait = true, ordering, ...operations }) {
        const response = await this._openApiClient.batchUpdate({
            collection_name,
            wait,
            ordering,
            ...operations,
        });
        return maybe(response.data.result).orThrow('Batch update returned empty');
    }
    /**
     * Recover from a snapshot
     * @param collection_name Name of the collection
     * @param shard_id Shard ID
     * @returns Operation result
     */
    async recoverShardFromSnapshot(collection_name, shard_id, { wait = true, ...shard_snapshot_recover }) {
        const response = await this._openApiClient.recoverShardFromSnapshot({
            collection_name,
            shard_id,
            wait,
            ...shard_snapshot_recover,
        });
        return maybe(response.data.result).orThrow('Recover shard from snapshot returned empty');
    }
    /**
     * Get list of snapshots for a shard of a collection
     * @param collection_name Name of the collection
     * @param shard_id Shard ID
     * @returns Operation result
     */
    async listShardSnapshots(collection_name, shard_id) {
        const response = await this._openApiClient.listShardSnapshots({
            collection_name,
            shard_id,
        });
        return maybe(response.data.result).orThrow('List shard snapshots returned empty');
    }
    /**
     * Create new snapshot of a shard for a collection
     * @param collection_name Name of the collection
     * @param shard_id Shard ID
     * @returns Operation result
     */
    async createShardSnapshot(collection_name, shard_id, { wait = true }) {
        const response = await this._openApiClient.createShardSnapshot({
            collection_name,
            shard_id,
            wait,
        });
        return maybe(response.data.result).orThrow('Create shard snapshot returned empty');
    }
    /**
     * Delete snapshot of a shard for a collection
     * @param collection_name Name of the collection
     * @param shard_id Shard ID
     * @param snapshot_name Snapshot name
     * @returns Operation result
     */
    async deleteShardSnapshot(collection_name, shard_id, snapshot_name, { wait = true }) {
        const response = await this._openApiClient.deleteShardSnapshot({
            collection_name,
            shard_id,
            snapshot_name,
            wait,
        });
        return maybe(response.data.result).orThrow('Create shard snapshot returned empty');
    }
    /**
     * Create shard key
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - shards_number: How many shards to create for this key If not specified, will use the default value from config
     *     - replication_factor: How many replicas to create for each shard If not specified, will use the default value from config
     *     - placement: Placement of shards for this key List of peer ids, that can be used to place shards for this key If not specified, will be randomly placed among all peers
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     * @returns Operation result
     */
    async createShardKey(collection_name, { shard_key, shards_number, replication_factor, placement, timeout, }) {
        const response = await this._openApiClient.createShardKey({
            collection_name,
            shard_key,
            shards_number,
            replication_factor,
            placement,
            timeout,
        });
        return maybe(response.data.result).orThrow('Create shard key returned empty');
    }
    /**
     * Delete shard key
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     * @returns Operation result
     */
    async deleteShardKey(collection_name, { shard_key, timeout }) {
        const response = await this._openApiClient.deleteShardKey({
            collection_name,
            shard_key,
            timeout,
        });
        return maybe(response.data.result).orThrow('Create shard key returned empty');
    }
    /**
     * Discover points
     * @description Use context and a target to find the most similar points to the target, constrained by the context.
     * When using only the context (without a target), a special search - called context search - is performed where pairs of points are used to generate a loss that guides the search towards the zone where most positive examples overlap. This means that the score minimizes the scenario of finding a point closer to a negative than to a positive part of a pair.
     * Since the score of a context relates to loss, the maximum score a point can get is 0.0, and it becomes normal that many points can have a score of 0.0.
     * When using target (with or without context), the score behaves a little different: The  integer part of the score represents the rank with respect to the context, while the decimal part of the score relates to the distance to the target. The context part of the score for  each pair is calculated +1 if the point is closer to a positive than to a negative part of a pair,  and -1 otherwise.
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards
     *     - target: Look for vectors closest to this. When using the target (with or without context), the integer part of the score represents the rank with respect to the context, while the decimal part of the score relates to the distance to the target.
     *     - context: Pairs of { positive, negative } examples to constrain the search. When using only the context (without a target), a special search - called context search - is performed where pairs of points are used to generate a loss that guides the search towards the zone where most positive examples overlap. This means that the score minimizes the scenario of finding a point closer to a negative than to a positive part of a pair. Since the score of a context relates to loss, the maximum score a point can get is 0.0, and it becomes normal that many points can have a score of 0.0. For discovery search (when including a target), the context part of the score for each pair is calculated +1 if the point is closer to a positive than to a negative part of a pair, and -1 otherwise.
     *     - filter: Look only for points which satisfies this conditions
     *     - params: Additional search params
     *     - limit: Max number of result to return
     *     - offset: Offset of the first result to return. May be used to paginate results. Note: large offset values may cause performance issues.
     *     - with_payload: Select which payload to return with the response
     *     - with_vector: Whether to return the point vector with the result?
     *     - using: Define which vector to use for recommendation, if not specified - try to use default vector
     *     - lookup_from The location used to lookup vectors. If not specified - use current collection. Note: the other collection should have the same vector size as the current collection
     * @returns Operation result
     */
    async discoverPoints(collection_name, { consistency, timeout, shard_key, target, context, params, limit, offset, with_payload, with_vector, using, lookup_from, }) {
        const response = await this._openApiClient.discoverPoints({
            collection_name,
            consistency,
            timeout,
            shard_key,
            target,
            context,
            params,
            limit,
            offset,
            with_payload,
            with_vector,
            using,
            lookup_from,
        });
        return maybe(response.data.result).orThrow('Discover points returned empty');
    }
    /**
     * Discover batch points
     * @description Look for points based on target and/or positive and negative example pairs, in batch.
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - searches: List of searches
     * @returns Operation result
     */
    async discoverBatchPoints(collection_name, { consistency, timeout, searches, }) {
        const response = await this._openApiClient.discoverBatchPoints({
            collection_name,
            consistency,
            timeout,
            searches,
        });
        return maybe(response.data.result).orThrow('Discover batch points returned empty');
    }
    /**
     * Returns information about the running Qdrant instance
     * @description Returns information about the running Qdrant instance like version and commit id
     * @returns Operation result
     */
    async versionInfo() {
        const response = await this._openApiClient.root({});
        return maybe(response.data).orThrow('Version Info returned empty');
    }
    /**
     * Check the existence of a collection
     * @param collection_name Name of the collection
     * @description Returns "true" if the given collection name exists, and "false" otherwise
     * @returns Operation result
     */
    async collectionExists(collection_name) {
        const response = await this._openApiClient.collectionExists({ collection_name });
        return maybe(response.data.result).orThrow('Collection exists returned empty');
    }
    /**
     * Query points
     * @description Universally query points. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards.
     *     - prefetch: Sub-requests to perform first. If present, the query will be performed on the results of the prefetch(es).
     *     - query: Query to perform. If missing without prefetches, returns points ordered by their IDs.
     *     - using: Define which vector name to use for querying. If missing, the default vector is used.
     *     - filter: Filter conditions - return only those points that satisfy the specified conditions.
     *     - params: Search params for when there is no prefetch
     *     - score_threshold: Return points with scores better than this threshold.
     *     - limit: Max number of points to return. Default is 10.
     *     - offset: Offset of the result. Skip this many points. Default is 0
     *     - with_vector: Options for specifying which vectors to include into the response. Default is false.
     *     - with_payload: Options for specifying which payload to include or not. Default is false.
     *     - lookup_from: The location to use for IDs lookup, if not specified - use the current collection and the 'using' vector Note: the other collection vectors should have the same vector size as the 'using' vector in the current collection.
     * @returns Operation result
     */
    async query(collection_name, { consistency, timeout, shard_key, prefetch, query, using, filter, params, score_threshold, limit, offset, with_vector, with_payload, lookup_from, }) {
        const response = await this._openApiClient.queryPoints({
            collection_name,
            consistency,
            timeout,
            shard_key,
            prefetch,
            query,
            using,
            filter,
            params,
            score_threshold,
            limit,
            offset,
            with_vector,
            with_payload,
            lookup_from,
        });
        return maybe(response.data.result).orThrow('Query points returned empty');
    }
    /**
     * Query points in batch
     * @description Universally query points in batch. This endpoint covers all capabilities of search, recommend, discover, filters. But also enables hybrid and multi-stage queries.
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - searches: List of queries
     * @returns Operation result
     */
    async queryBatch(collection_name, { consistency, timeout, searches, }) {
        const response = await this._openApiClient.queryBatchPoints({
            collection_name,
            consistency,
            timeout,
            searches,
        });
        return maybe(response.data.result).orThrow('Query points returned empty');
    }
    /**
     * Query points, grouped by a given payload field
     * @description Universally query points, grouped by a given payload field
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards.
     *     - prefetch: Sub-requests to perform first. If present, the query will be performed on the results of the prefetch(es).
     *     - query: Query to perform. If missing without prefetches, returns points ordered by their IDs.
     *     - using: Define which vector name to use for querying. If missing, the default vector is used.
     *     - filter: Filter conditions - return only those points that satisfy the specified conditions.
     *     - params: Search params for when there is no prefetch
     *     - score_threshold: Return points with scores better than this threshold.
     *     - with_vector: Options for specifying which vectors to include into the response. Default is false.
     *     - with_payload: Options for specifying which payload to include or not. Default is false.
     *     - group_by: Payload field to group by, must be a string or number field. If the field contains more than 1 value, all values will be used for grouping. One point can be in multiple groups.
     *     - group_size: Maximum amount of points to return per group. Default is 3.
     *     - limit: Maximum amount of groups to return. Default is 10.
     *     - with_lookup: Look for points in another collection using the group ids.
     * @returns Operation result
     */
    async queryGroups(collection_name, { consistency, timeout, shard_key, prefetch, query, using, filter, params, score_threshold, with_vector, with_payload, group_by, group_size, limit, with_lookup, }) {
        const response = await this._openApiClient.queryPointsGroups({
            collection_name,
            consistency,
            timeout,
            shard_key,
            prefetch,
            query,
            using,
            filter,
            params,
            score_threshold,
            with_vector,
            with_payload,
            group_by,
            group_size,
            limit,
            with_lookup,
        });
        return maybe(response.data.result).orThrow('Query groups returned empty');
    }
    /**
     * Facet a payload key with a given filter.
     * @description Count points that satisfy the given filter for each unique value of a payload key.
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards.
     *     - key: Payload key to use for faceting.
     *     - limit: Max number of hits to return. Default is 10.
     *     - filter: Filter conditions - only consider points that satisfy these conditions.
     *     - exact: Whether to do a more expensive exact count for each of the values in the facet. Default is false.
     * @returns Operation result
     */
    async facet(collection_name, { consistency, timeout, shard_key, key, limit, filter, exact, }) {
        const response = await this._openApiClient.facet({
            collection_name,
            consistency,
            timeout,
            shard_key,
            key,
            limit,
            filter,
            exact,
        });
        return maybe(response.data.result).orThrow('Facet returned empty');
    }
    /**
     * Search points matrix distance pairs.
     * @description Compute distance matrix for sampled points with a pair based output format.
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards.
     *     - filter: Look only for points which satisfies this conditions.
     *     - sample: How many points to select and search within. Default is 10.
     *     - limit: How many neighbours per sample to find. Default is 3.
     *     - using: Define which vector name to use for querying. If missing, the default vector is used.
     * @returns Operation result
     */
    async searchMatrixPairs(collection_name, { consistency, timeout, shard_key, filter, sample, limit, using, }) {
        const response = await this._openApiClient.searchMatrixPairs({
            collection_name,
            consistency,
            timeout,
            shard_key,
            filter,
            sample,
            limit,
            using,
        });
        return maybe(response.data.result).orThrow('Search points matrix pairs returned empty');
    }
    /**
     * Search points matrix distance offsets.
     * @description Compute distance matrix for sampled points with an offset based output format.
     * @param collection_name Name of the collection
     * @param {object} args -
     *     - consistency: Read consistency of the search. Defines how many replicas should be queried before returning the result.
     *         Values:
     *             number - number of replicas to query, values should present in all queried replicas
     *             'majority' - query all replicas, but return values present in the majority of replicas
     *             'quorum' - query the majority of replicas, return values present in all of them
     *             'all' - query all replicas, and return values present in all replicas
     *     - timeout: If set, overrides global timeout setting for this request. Unit is seconds.
     *     - shard_key: Specify in which shards to look for the points, if not specified - look in all shards.
     *     - filter: Look only for points which satisfies this conditions.
     *     - sample: How many points to select and search within. Default is 10.
     *     - limit: How many neighbours per sample to find. Default is 3.
     *     - using: Define which vector name to use for querying. If missing, the default vector is used.
     * @returns Operation result
     */
    async searchMatrixOffsets(collection_name, { consistency, timeout, shard_key, filter, sample, limit, using, }) {
        const response = await this._openApiClient.searchMatrixOffsets({
            collection_name,
            consistency,
            timeout,
            shard_key,
            filter,
            sample,
            limit,
            using,
        });
        return maybe(response.data.result).orThrow('Search points matrix offsets returned empty');
    }
}

exports.QdrantClient = QdrantClient;
exports.QdrantClientConfigError = QdrantClientConfigError;
exports.QdrantClientResourceExhaustedError = QdrantClientResourceExhaustedError;
exports.QdrantClientTimeoutError = QdrantClientTimeoutError;
exports.QdrantClientUnexpectedResponseError = QdrantClientUnexpectedResponseError;
