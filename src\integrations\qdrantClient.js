import { QdrantClient } from '@qdrant/js-client-rest';
import { config } from '../config.js';
import { logger } from '../utils/logger.js';

const { qdrant, features } = config;

export class VectorStore {
  constructor() {
    this.enabled = features.ragEnabled;
    if (this.enabled) {
      this.client = new QdrantClient({
        url: qdrant.url,
        apiKey: qdrant.apiKey || undefined,
      });
    }
    this.initialised = false;
  }

  async init() {
    if (!this.enabled) {
      return;
    }
    if (this.initialised) {
      return;
    }

    try {
      await this.client.getCollection(qdrant.collection);
    } catch (error) {
      logger.info({ collection: qdrant.collection }, 'Creating Qdrant collection');
      await this.client.createCollection(qdrant.collection, {
        vectors: {
          size: qdrant.vectorSize,
          distance: qdrant.distance,
        },
      });
    }

    this.initialised = true;
  }

  async upsert({ id, vector, payload }) {
    if (!this.enabled) {
      return;
    }
    await this.init();
    try {
      await this.client.upsert(qdrant.collection, {
        wait: false,
        points: [
          {
            id,
            vector,
            payload,
          },
        ],
      });
    } catch (error) {
      logger.error({ error }, 'Failed to upsert point into Qdrant');
    }
  }

  async query({ vector, limit = qdrant.queryLimit, filter }) {
    if (!this.enabled) {
      return [];
    }
    await this.init();
    try {
      const response = await this.client.search(qdrant.collection, {
        vector,
        limit,
        filter,
        with_payload: true,
      });
      return response || [];
    } catch (error) {
      logger.error({ error }, 'Failed to query Qdrant');
      return [];
    }
  }
}

export const vectorStore = new VectorStore();
