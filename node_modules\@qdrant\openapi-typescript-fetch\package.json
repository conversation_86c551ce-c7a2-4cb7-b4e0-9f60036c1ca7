{"name": "@qdrant/openapi-typescript-fetch", "description": "A typed fetch client for openapi-typescript", "version": "1.2.6", "engines": {"node": ">=18.0.0", "pnpm": ">=8"}, "packageManager": "pnpm@8.5.0", "author": "<PERSON><PERSON><PERSON>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/Rendez"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "javi<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "browser": "./dist/es6/index.js", "require": "./dist/cjs/index.js", "default": "./dist/esm/index.js"}}, "types": "./dist/index.d.ts", "browser": "./dist/es6/index.js", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/qdrant/openapi-typescript-fetch"}, "keywords": ["fetch", "client", "swagger", "typescript", "ts", "openapi", "openapi 3", "node"], "bugs": {"url": "https://github.com/qdrant/openapi-typescript-fetch/issues"}, "homepage": "https://github.com/qdrant/openapi-typescript-fetch#readme", "devDependencies": {"@changesets/changelog-github": "0.4.8", "@changesets/cli": "2.26.1", "@types/jest": "^29.5.12", "@types/node": "^18.0.0", "@types/semver": "^7.5.7", "@typescript-eslint/eslint-plugin": "^4.30.0", "@typescript-eslint/parser": "^4.31.0", "codecov": "^3.8.2", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^0.49.3", "prettier": "^2.4.0", "rimraf": "^3.0.0", "semver": "^7.6.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": ">=4.7", "whatwg-fetch": "^3.6.2"}, "prettier": {"trailingComma": "all", "singleQuote": true, "semi": false}, "scripts": {"clean": "rimraf './dist'", "build": "pnpm clean && tsc -p tsconfig.json && tsc -p tsconfig.es6.json && tsc -p tsconfig.cjs.json && node post-build.js", "lint": "eslint .", "test": "jest", "test:coverage": "pnpm build && jest --no-cache --coverage && codecov", "test:coverage:local": "pnpm build && jest --no-cache --collectCoverage", "tsc:check": "tsc --noEmit", "ci:version": "pnpm changeset version && pnpm install --no-frozen-lockfile && git add .", "ci:release": "pnpm changeset publish"}}