import axios from 'axios';
import { config } from '../config.js';
import { logger } from '../utils/logger.js';

const { ai, features } = config;

export class Embedder {
  constructor() {
    this.client = axios.create({
      baseURL: ai.baseUrl,
      timeout: 30_000,
    });
    this.disabled = !features.ragEnabled;
  }

  async embed(input) {
    if (this.disabled || !features.ragEnabled) {
      return null;
    }

    const inputs = Array.isArray(input) ? input : [input];
    const payload = {
      model: ai.embeddingModel,
      input: inputs,
    };

    try {
      const { data } = await this.client.post(ai.embeddingEndpoint, payload);
      const embeddings = data?.data?.map((item) => item.embedding);
      if (!embeddings || embeddings.length === 0) {
        throw new Error('Embedding response missing data');
      }
      return Array.isArray(input) ? embeddings : embeddings[0];
    } catch (error) {
      const status = error.response?.status;
      if (status === 404) {
        logger.warn('Embedding endpoint returned 404, disabling RAG features. Set RAG_ENABLED=false to hide this warning.');
        this.disabled = true;
        return null;
      }
      logger.error({ error }, 'Failed to compute embeddings');
      throw error;
    }
  }
}

export const embedder = new Embedder();
