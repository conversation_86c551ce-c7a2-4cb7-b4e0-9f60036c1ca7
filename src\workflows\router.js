import { randomUUID } from 'crypto';
import { llmClient } from '../ai/llmClient.js';
import { embedder } from '../ai/embedder.js';
import { vectorStore } from '../integrations/qdrantClient.js';
import { config } from '../config.js';
import { logger } from '../utils/logger.js';

export class MessageRouter {
  constructor({ whatsapp, telegram, messageStore }) {
    this.whatsapp = whatsapp;
    this.telegram = telegram;
    this.messageStore = messageStore;
    this.pendingReplies = new Map();
  }

  async handleIncoming(message) {
    const contact = await message.getContact();
    const chat = await message.getChat();
    const contactName = contact.pushname || contact.name || contact.number;
    const chatName = chat.isGroup ? chat.name : contactName;
    const chatId = message.from;
    const timestamp = message.timestamp ? message.timestamp * 1000 : Date.now();
    const serializedId = message.id?._serialized || randomUUID();
    const body = typeof message.body === 'string' ? message.body : '';

    await this.messageStore.addMessage({
      chatId,
      messageId: serializedId,
      author: contactName,
      body,
      timestamp,
      direction: 'inbound',
    });

    if (body.trim()) {
      await this.indexMessage({
        chatId,
        chatName,
        contactName,
        body,
        timestamp,
        messageId: serializedId,
      });
    }

    const summary = await this.classifyMessage({
      body,
      from: contactName,
      chatId,
      chatName,
    });

    const replyContext = await this.buildReplyContext({
      chatId,
      query: body,
      contactName,
      chatName,
    });

    const baseText = this.buildTelegramAlert({ contactName, chatName, summary, body });

    if (summary.shouldReply) {
      const reply = await this.generateReply({
        message: body,
        contextLabel: chatName,
        contextText: replyContext,
      });
      const requestId = randomUUID();
      this.pendingReplies.set(requestId, { chatId, reply });

      const notificationText = baseText
        + '\n\nContesto usato:\n' + (replyContext || 'nessun messaggio rilevante')
        + '\n\nRisposta suggerita:\n' + reply;

      await this.telegram.notify({
        text: notificationText,
        buttons: [
          { text: 'Invia risposta', callback_data: 'confirm:' + requestId },
          { text: 'Annulla', callback_data: 'cancel:' + requestId },
        ],
      });

      if (config.features.autoReply) {
        await this.dispatchReply(requestId);
      }
    } else {
      await this.telegram.notify({ text: baseText });
    }
  }

  async handleOwnerTelegramMessage(msg) {
    if (!config.features.telegramAssistant) {
      return;
    }
    const prompt = msg.text?.trim();
    if (!prompt) {
      return;
    }

    const context = await this.retrieveGlobalContext(prompt);
    const answer = await this.answerOwnerQuestion({ prompt, context });
    await this.telegram.notify({ text: answer });
  }

  buildTelegramAlert({ contactName, chatName, summary, body }) {
    return [
      'Chat: ' + chatName,
      'Da: ' + contactName,
      'Importanza stimata: ' + summary.importance,
      summary.summary ? 'Riassunto: ' + summary.summary : null,
      summary.reason ? 'Motivo: ' + summary.reason : null,
      '---',
      body,
    ]
      .filter(Boolean)
      .join('\n');
  }

  async classifyMessage({ body, from, chatId, chatName }) {
    try {
      const contextMessages = this.messageStore.getRecentMessages(chatId);
      const contextText = this.formatMessages(contextMessages);
      const { content } = await llmClient.chat({
        systemPrompt: 'Sei un assistente che valuta messaggi WhatsApp per importanza e azione necessaria. Rispondi solo con JSON valido.',
        userPrompt: 'Analizza il seguente messaggio e restituisci un oggetto JSON con le chiavi importance (high|medium|low), shouldReply (true|false), summary (breve riassunto in italiano) e reason.\n\nDa: ' + from + '\nChat: ' + chatName + '\nMessaggio: ' + body + '\n\nContesto recente:\n' + contextText,
      });

      const parsed = JSON.parse(content);
      return {
        importance: parsed.importance || 'medium',
        shouldReply: Boolean(parsed.shouldReply),
        summary: parsed.summary || '',
        reason: parsed.reason || '',
      };
    } catch (error) {
      logger.warn({ error }, 'Failed to classify message with LLM, fallback to defaults');
      const safeBody = body || '';
      return {
        importance: safeBody.includes('!') ? 'high' : 'medium',
        shouldReply: safeBody.includes('?'),
        summary: safeBody.slice(0, 120),
        reason: 'fallback heuristic',
      };
    }
  }

  async generateReply({ message, contextLabel, contextText }) {
    try {
      const { content } = await llmClient.chat({
        systemPrompt: 'Sei il mio assistente personale. Rispondi in italiano in modo conciso e gentile. Usa il contesto solo se rilevante e non inventare fatti.',
        userPrompt: 'Chat: ' + contextLabel + '\nMessaggio: ' + message + '\n\nContesto disponibile:\n' + (contextText || 'Contesto non disponibile.') + '\n\nScrivi una risposta breve e naturale come se fossi me.',
      });
      return content;
    } catch (error) {
      logger.error({ error }, 'Failed to generate reply');
      throw error;
    }
  }

  async dispatchReply(requestId) {
    const pending = this.pendingReplies.get(requestId);
    if (!pending) {
      logger.warn({ requestId }, 'No pending reply found');
      return;
    }

    await this.whatsapp.sendMessage(pending.chatId, pending.reply);
    this.pendingReplies.delete(requestId);
    await this.telegram.notify({ text: 'Risposta inviata su WhatsApp.' });
  }

  formatMessages(messages) {
    if (!messages?.length) {
      return '';
    }
    return messages
      .map((msg) => {
        const date = new Date(msg.timestamp || Date.now()).toISOString();
        const author = msg.author || 'Sconosciuto';
        return '[' + date + '] ' + author + ': ' + msg.body;
      })
      .join('\n');
  }

  async buildReplyContext({ chatId, query, contactName, chatName }) {
    const recentFormatted = this.formatMessages(this.messageStore.getRecentMessages(chatId));
    let ragFormatted = '';

    if (query?.trim()) {
      try {
        const vector = await embedder.embed(query);
        const matches = await vectorStore.query({
          vector,
          filter: {
            must: [
              {
                key: 'chatId',
                match: { value: chatId },
              },
            ],
          },
        });
        ragFormatted = matches
          .map((match) => {
            const payload = match.payload || {};
            const date = payload.timestamp ? new Date(payload.timestamp).toISOString() : 's.d.';
            const author = payload.contactName || contactName;
            return '[' + date + '] ' + author + ': ' + payload.body;
          })
          .join('\n');
      } catch (error) {
        logger.warn({ error }, 'Failed to gather RAG context for reply');
      }
    }

    const parts = [];
    if (recentFormatted) {
      parts.push('Messaggi recenti in chat "' + chatName + '":\n' + recentFormatted);
    }
    if (ragFormatted) {
      parts.push('Messaggi simili trovati:\n' + ragFormatted);
    }

    return parts.join('\n\n');
  }

  async indexMessage({ chatId, chatName, contactName, body, timestamp, messageId }) {
    try {
      const vector = await embedder.embed(body);
      if (!vector) {
        return;
      }
      if (!Array.isArray(vector)) {
        throw new Error('Embedding result is not an array');
      }
      if (vector.length !== config.qdrant.vectorSize) {
        logger.warn({ expected: config.qdrant.vectorSize, received: vector.length }, 'Embedding size does not match Qdrant configuration');
      }
      await vectorStore.upsert({
        id: messageId,
        vector,
        payload: {
          chatId,
          chatName,
          contactName,
          body,
          timestamp,
        },
      });
    } catch (error) {
      logger.warn({ error }, 'Failed to index message in Qdrant');
    }
  }

  async retrieveGlobalContext(prompt) {
    const allMessages = this.messageStore.getAllMessages();
    let ragMatches = [];

    try {
      const vector = await embedder.embed(prompt);
      if (vector) {
        ragMatches = await vectorStore.query({ vector, limit: config.qdrant.queryLimit });
      }
    } catch (error) {
      logger.warn({ error }, 'Global context retrieval failed');
    }

    const ragText = ragMatches
      .map((match) => {
        const payload = match.payload || {};
        const date = payload.timestamp ? new Date(payload.timestamp).toISOString() : 's.d.';
        const chatLabel = payload.chatName || payload.contactName || payload.chatId || 'Chat sconosciuta';
        const author = payload.contactName || 'Sconosciuto';
        return '[' + date + '] (' + chatLabel + ') ' + author + ': ' + payload.body;
      })
      .join('\n');

    const latest = allMessages
      .slice()
      .sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0))
      .slice(0, 15)
      .map((msg) => {
        const date = new Date(msg.timestamp || Date.now()).toISOString();
        return '[' + date + '] (' + msg.chatId + ') ' + (msg.author || 'Sconosciuto') + ': ' + msg.body;
      })
      .join('\n');

    return [
      'Estratti vettore rilevanti:', ragText || 'Nessun match trovato.',
      '',
      'Messaggi più recenti:', latest || 'Nessun messaggio memorizzato.',
    ].join('\n');
  }

  async answerOwnerQuestion({ prompt, context }) {
    try {
      const { content } = await llmClient.chat({
        systemPrompt: 'Sei un assistente personale che risponde basandosi sul contesto delle chat WhatsApp fornite. Se il contesto non contiene la risposta, dichiaralo esplicitamente.',
        userPrompt: 'Domanda: ' + prompt + '\n\nContesto disponibile:\n' + context,
      });
      return content || 'Non ho trovato informazioni rilevanti nel contesto.';
    } catch (error) {
      logger.error({ error }, 'Failed to answer Telegram question');
      return 'Errore durante la generazione della risposta.';
    }
  }
}
