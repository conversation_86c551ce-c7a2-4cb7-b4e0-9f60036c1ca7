import { logger } from './utils/logger.js';
import { WhatsAppChannel } from './channels/whatsapp.js';
import { TelegramChannel } from './channels/telegram.js';
import { MessageRouter } from './workflows/router.js';
import { MessageStore } from './storage/messageStore.js';
import { config } from './config.js';
import { vectorStore } from './integrations/qdrantClient.js';

async function main() {
  logger.info('Bootstrapping WhatsApp assistant');

  const messageStore = new MessageStore({
    filePath: config.persistence.messageStorePath,
    maxMessagesPerChat: config.persistence.maxMessagesPerChat,
  });
  await messageStore.init();

  if (config.features.ragEnabled) {
    await vectorStore.init();
  }

  let router;
  const whatsapp = new WhatsAppChannel({
    onMessage: (message) => router.handleIncoming(message),
    onReady: () => logger.info('WhatsApp ready, listening for messages'),
  });

  const telegram = new TelegramChannel({
    onConfirmReply: ({ requestId }) => router.dispatchReply(requestId),
    onOwnerMessage: (msg) => router.handleOwnerTelegramMessage(msg),
  });

  router = new MessageRouter({ whatsapp, telegram, messageStore });

  await whatsapp.init();
  await telegram.notify({ text: 'Assistente avviato e in ascolto.' });
}

main().catch((error) => {
  logger.error({ error }, 'Fatal error while running assistant');
  process.exit(1);
});
