import { Maybe } from './maybe';
declare function all<T1>(maybies: [Maybe<T1>]): Maybe<[T1]>;
declare function all<T1, T2>(maybies: [Maybe<T1>, Maybe<T2>]): Maybe<[T1, T2]>;
declare function all<T1, T2, T3>(maybies: [Maybe<T1>, Maybe<T2>, Maybe<T3>]): Maybe<[T1, T2, T3]>;
declare function all<T1, T2, T3, T4>(maybies: [Maybe<T1>, Maybe<T2>, Maybe<T3>, Maybe<T4>]): Maybe<[T1, T2, T3, T4]>;
declare function all<T1, T2, T3, T4, T5>(maybies: [Maybe<T1>, Maybe<T2>, Maybe<T3>, Maybe<T4>, Maybe<T5>]): Maybe<[T1, T2, T3, T4, T5]>;
declare function all<T1, T2, T3, T4, T5, T6>(maybies: [Maybe<T1>, Maybe<T2>, Maybe<T3>, Maybe<T4>, Maybe<T5>, Maybe<T6>]): Maybe<[T1, T2, T3, T4, T5, T6]>;
declare function all<T1, T2, T3, T4, T5, T6, T7>(maybies: [Maybe<T1>, Maybe<T2>, Maybe<T3>, Maybe<T4>, Maybe<T5>, Maybe<T6>, Maybe<T7>]): Maybe<[T1, T2, T3, T4, T5, T6, T7]>;
declare function all<T1, T2, T3, T4, T5, T6, T7, T8>(maybies: [Maybe<T1>, Maybe<T2>, Maybe<T3>, Maybe<T4>, Maybe<T5>, Maybe<T6>, Maybe<T7>, Maybe<T8>]): Maybe<[T1, T2, T3, T4, T5, T6, T7, T8]>;
declare function all<T1, T2, T3, T4, T5, T6, T7, T8, T9>(maybies: [Maybe<T1>, Maybe<T2>, Maybe<T3>, Maybe<T4>, Maybe<T5>, Maybe<T6>, Maybe<T7>, Maybe<T8>, Maybe<T9>]): Maybe<[T1, T2, T3, T4, T5, T6, T7, T8, T9]>;
declare function all<T1, T2, T3, T4, T5, T6, T7, T8, T9, T10>(maybies: [Maybe<T1>, Maybe<T2>, Maybe<T3>, Maybe<T4>, Maybe<T5>, Maybe<T6>, Maybe<T7>, Maybe<T8>, Maybe<T9>, Maybe<T10>]): Maybe<[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]>;
declare function all<T>(maybies: Maybe<T>[]): Maybe<T[]>;
export { all };
