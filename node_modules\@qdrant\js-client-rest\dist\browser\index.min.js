var t={isNone:function(){return!0},orElse:function(t){return t},orCall:function(t){return t()},orNull:function(){return null},orThrow:function(t){throw void 0===t&&(t="Unexpected null value"),new TypeError(t)},map:function(){return t},get:function(){return t}},e=function(){function t(t){this.value=t}return t.prototype.isNone=function(){return!1},t.prototype.orElse=function(){return this.value},t.prototype.orCall=function(){return this.value},t.prototype.orNull=function(){return this.value},t.prototype.orThrow=function(){return this.value},t.prototype.map=function(t){return o(t(this.value))},t.prototype.get=function(t){return this.map((function(e){return e[t]}))},t}();function o(o){return function(o){return o===t||o instanceof e}(o)?o:null==o?t:function(t){if(null==t)throw new TypeError("some() does not accept null or undefined");return new e(t)}(o)}class r extends Error{constructor(t){super(t.statusText),Object.setPrototypeOf(this,new.target.prototype),this.headers=t.headers,this.url=t.url,this.status=t.status,this.statusText=t.statusText,this.data=t.data}}let n,a;"rawJSON"in JSON&&(n=function(t,e,o){if(Number.isInteger(e)&&!Number.isSafeInteger(e))try{return BigInt(o.source)}catch{return e}return e},a=function(t,e){return"bigint"==typeof e?JSON.rawJSON(String(e)):e});const i=t=>"post"===t||"put"===t||"patch"===t||"delete"===t;function s(t,e,o){let r={};return i(t)?o.forEach((t=>{r[t]=e[t],delete e[t]})):r={...e},function(t){const e=[],o=(t,e)=>`${encodeURIComponent(t)}=${encodeURIComponent(String(e))}`;return Object.keys(t).forEach((r=>{const n=t[r];null!=n&&(Array.isArray(n)?n.forEach((t=>e.push(o(r,t)))):e.push(o(r,n)))})),e.length>0?`?${e.join("&")}`:""}(r)}function c(t,e){const o=new Headers(t?.headers),r=new Headers(e?.headers);for(const t of r.keys()){const e=r.get(t);null!=e&&o.set(t,e)}return{...t,...e,headers:o}}function l(t){const e=Object.assign(Array.isArray(t.payload)?[]:{},t.payload),o=function(t,e){return t.replace(/\{([^}]+)\}/g,((t,o)=>{const r=encodeURIComponent(e[o]);return delete e[o],r}))}(t.path,e),r=s(t.method,e,t.queryParams),n=function(t,e){if(!i(t))return;const o=e instanceof FormData?e:JSON.stringify(e,a);return"delete"===t&&"{}"===o?void 0:o}(t.method,e),c=i(t.method)?function(t,e){const o=new Headers(e);return void 0===t||t instanceof FormData||o.has("Content-Type")||o.append("Content-Type","application/json"),o.has("Accept")||o.append("Accept","application/json"),o}(n,t.init?.headers):new Headers(t.init?.headers);return{url:t.baseUrl+o+r,init:{...t.init,method:t.method.toUpperCase(),headers:c,body:n}}}async function h(t,e){const o=await fetch(t,e),a=await async function(t){if(204===t.status)return;const e=t.headers.get("content-type"),o=await t.text();if(e&&e.includes("application/json"))return JSON.parse(o,n);try{return JSON.parse(o,n)}catch(t){return o}}(o),i={headers:o.headers,url:o.url,ok:o.ok,status:o.status,statusText:o.statusText,data:a};if(i.ok)return i;throw new r(i)}function p(){let t="",e={};const o=[],n=function(t,e){const o=async(r,n,a)=>{if(null==t||r===t.length)return e(n,a);const i=t[r];return await i(n,a,((t,e)=>o(r+1,t,e)))};return(t,e)=>o(0,t,e)}(o,h);return{configure:r=>{t=r.baseUrl||"",e=r.init||{},o.splice(0),o.push(...r.use||[])},use:t=>o.push(t),path:o=>({method:a=>({create:i=>function(t){const e=async(o,n)=>{try{return await t(o,n)}catch(t){if(t instanceof r)throw new e.Error(t);throw t}};return e.Error=class extends r{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}getActualType(){return{status:this.status,data:this.data}}},e}(((r,s)=>async function(t){const{url:e,init:o}=l(t);return await t.fetch(e,o)}({baseUrl:t||"",path:o,method:a,queryParams:Object.keys(i||{}),payload:r,init:c(e,s),fetch:n})))})})}}const d=()=>p();class u extends Error{constructor(t){super(t),this.name=this.constructor.name,Object.setPrototypeOf(this,new.target.prototype)}}class m extends u{static forResponse(t){const e=`${`${t.status}`} ${t.statusText?`(${t.statusText})`:"(Unrecognized Status Code)"}`.trim(),o=t.data?JSON.stringify(t.data,null,2):null;let r="";o&&(r=o.length<=200?o:o.slice(0,-4)+" ...");return new m(`Unexpected Response: ${e}\n${`Raw response content:\n${r}`}`)}}class _ extends u{}class y extends u{}class w extends u{constructor(t,e){super(t);const o=Number(e);if(isNaN(o))throw new u(`Invalid retryAfter value: ${e}`);this.retry_after=o,Object.setPrototypeOf(this,new.target.prototype)}}function f(t,e){const o=function(t,{headers:e,timeout:o,connections:n}){const a=[];Number.isFinite(o)&&a.push((async(t,e,r)=>{const n=new AbortController,a=setTimeout((()=>n.abort()),o);try{return await r(t,Object.assign(e,{signal:n.signal}))}catch(t){if(t instanceof Error&&"AbortError"===t.name)throw new y(t.message);throw t}finally{clearTimeout(a)}}));a.push((async(t,e,o)=>{let n;try{if(n=await o(t,e),200===n.status||201===n.status)return n}catch(t){if(t instanceof r&&429===t.status){const e=t.headers.get("retry-after")?.[0];if(e)throw new w(t.message,e)}throw t}throw m.forResponse(n)}));const i=d();return i.configure({baseUrl:t,init:{headers:e,dispatcher:void 0},use:a}),i}(t,e);return function(t){return{createShardKey:t.path("/collections/{collection_name}/shards").method("put").create({timeout:!0}),deleteShardKey:t.path("/collections/{collection_name}/shards/delete").method("post").create({timeout:!0}),root:t.path("/").method("get").create(),telemetry:t.path("/telemetry").method("get").create(),metrics:t.path("/metrics").method("get").create(),getLocks:t.path("/locks").method("get").create(),postLocks:t.path("/locks").method("post").create(),healthz:t.path("/healthz").method("get").create(),livez:t.path("/livez").method("get").create(),readyz:t.path("/readyz").method("get").create(),getIssues:t.path("/issues").method("get").create(),clearIssues:t.path("/issues").method("delete").create(),clusterStatus:t.path("/cluster").method("get").create(),recoverCurrentPeer:t.path("/cluster/recover").method("post").create(),removePeer:t.path("/cluster/peer/{peer_id}").method("delete").create({force:!0}),getCollections:t.path("/collections").method("get").create(),getCollection:t.path("/collections/{collection_name}").method("get").create(),createCollection:t.path("/collections/{collection_name}").method("put").create({timeout:!0}),deleteCollection:t.path("/collections/{collection_name}").method("delete").create({timeout:!0}),updateCollection:t.path("/collections/{collection_name}").method("patch").create({timeout:!0}),updateAliases:t.path("/collections/aliases").method("post").create({timeout:!0}),createFieldIndex:t.path("/collections/{collection_name}/index").method("put").create({wait:!0,ordering:!0}),collectionExists:t.path("/collections/{collection_name}/exists").method("get").create(),deleteFieldIndex:t.path("/collections/{collection_name}/index/{field_name}").method("delete").create({wait:!0,ordering:!0}),collectionClusterInfo:t.path("/collections/{collection_name}/cluster").method("get").create(),updateCollectionCluster:t.path("/collections/{collection_name}/cluster").method("post").create({timeout:!0}),getCollectionAliases:t.path("/collections/{collection_name}/aliases").method("get").create(),getCollectionsAliases:t.path("/aliases").method("get").create(),recoverFromUploadedSnapshot:t.path("/collections/{collection_name}/snapshots/upload").method("post").create({wait:!0,priority:!0,checksum:!0}),recoverFromSnapshot:t.path("/collections/{collection_name}/snapshots/recover").method("put").create({wait:!0}),listSnapshots:t.path("/collections/{collection_name}/snapshots").method("get").create(),createSnapshot:t.path("/collections/{collection_name}/snapshots").method("post").create({wait:!0}),getSnapshot:t.path("/collections/{collection_name}/snapshots/{snapshot_name}").method("get").create(),deleteSnapshot:t.path("/collections/{collection_name}/snapshots/{snapshot_name}").method("delete").create({wait:!0}),listFullSnapshots:t.path("/snapshots").method("get").create(),createFullSnapshot:t.path("/snapshots").method("post").create({wait:!0}),getFullSnapshot:t.path("/snapshots/{snapshot_name}").method("get").create(),deleteFullSnapshot:t.path("/snapshots/{snapshot_name}").method("delete").create({wait:!0}),recoverShardFromUploadedSnapshot:t.path("/collections/{collection_name}/shards/{shard_id}/snapshots/upload").method("post").create({wait:!0,priority:!0,checksum:!0}),recoverShardFromSnapshot:t.path("/collections/{collection_name}/shards/{shard_id}/snapshots/recover").method("put").create({wait:!0}),listShardSnapshots:t.path("/collections/{collection_name}/shards/{shard_id}/snapshots").method("get").create(),createShardSnapshot:t.path("/collections/{collection_name}/shards/{shard_id}/snapshots").method("post").create({wait:!0}),getShardSnapshot:t.path("/collections/{collection_name}/shards/{shard_id}/snapshots/{snapshot_name}").method("get").create(),deleteShardSnapshot:t.path("/collections/{collection_name}/shards/{shard_id}/snapshots/{snapshot_name}").method("delete").create({wait:!0}),getPoint:t.path("/collections/{collection_name}/points/{id}").method("get").create(),upsertPoints:t.path("/collections/{collection_name}/points").method("put").create({wait:!0,ordering:!0}),getPoints:t.path("/collections/{collection_name}/points").method("post").create({consistency:!0,timeout:!0}),deletePoints:t.path("/collections/{collection_name}/points/delete").method("post").create({wait:!0,ordering:!0}),updateVectors:t.path("/collections/{collection_name}/points/vectors").method("put").create({wait:!0,ordering:!0}),deleteVectors:t.path("/collections/{collection_name}/points/vectors/delete").method("post").create({wait:!0,ordering:!0}),overwritePayload:t.path("/collections/{collection_name}/points/payload").method("put").create({wait:!0,ordering:!0}),setPayload:t.path("/collections/{collection_name}/points/payload").method("post").create({wait:!0,ordering:!0}),deletePayload:t.path("/collections/{collection_name}/points/payload/delete").method("post").create({wait:!0,ordering:!0}),clearPayload:t.path("/collections/{collection_name}/points/payload/clear").method("post").create({wait:!0,ordering:!0}),batchUpdate:t.path("/collections/{collection_name}/points/batch").method("post").create({wait:!0,ordering:!0}),scrollPoints:t.path("/collections/{collection_name}/points/scroll").method("post").create({consistency:!0,timeout:!0}),searchPoints:t.path("/collections/{collection_name}/points/search").method("post").create({consistency:!0,timeout:!0}),searchBatchPoints:t.path("/collections/{collection_name}/points/search/batch").method("post").create({consistency:!0,timeout:!0}),searchPointGroups:t.path("/collections/{collection_name}/points/search/groups").method("post").create({consistency:!0,timeout:!0}),recommendPoints:t.path("/collections/{collection_name}/points/recommend").method("post").create({consistency:!0,timeout:!0}),recommendBatchPoints:t.path("/collections/{collection_name}/points/recommend/batch").method("post").create({consistency:!0,timeout:!0}),recommendPointGroups:t.path("/collections/{collection_name}/points/recommend/groups").method("post").create({consistency:!0,timeout:!0}),discoverPoints:t.path("/collections/{collection_name}/points/discover").method("post").create({consistency:!0,timeout:!0}),discoverBatchPoints:t.path("/collections/{collection_name}/points/discover/batch").method("post").create({consistency:!0,timeout:!0}),countPoints:t.path("/collections/{collection_name}/points/count").method("post").create({timeout:!0}),facet:t.path("/collections/{collection_name}/facet").method("post").create({timeout:!0,consistency:!0}),queryPoints:t.path("/collections/{collection_name}/points/query").method("post").create({consistency:!0,timeout:!0}),queryBatchPoints:t.path("/collections/{collection_name}/points/query/batch").method("post").create({consistency:!0,timeout:!0}),queryPointsGroups:t.path("/collections/{collection_name}/points/query/groups").method("post").create({consistency:!0,timeout:!0}),searchMatrixPairs:t.path("/collections/{collection_name}/points/search/matrix/pairs").method("post").create({consistency:!0,timeout:!0}),searchMatrixOffsets:t.path("/collections/{collection_name}/points/search/matrix/offsets").method("post").create({consistency:!0,timeout:!0})}}(o)}const g="1.15.1",C={parseVersion(t){if(!t)throw new Error("Version is null");let e,o;if([e,o]=t.split(".",2),e=parseInt(e,10),o=parseInt(o,10),isNaN(e)||isNaN(o))throw new Error(`Unable to parse version, expected format: x.y[.z], found: ${t}`);return{major:e,minor:o}},isCompatible(t,e){if(!t||!e)return console.debug(`Unable to compare versions with null values. Client: ${t}, Server: ${e}`),!1;if(t===e)return!0;try{const o=C.parseVersion(t),r=C.parseVersion(e);return o.major===r.major&&Math.abs(o.minor-r.minor)<=1}catch(t){return console.debug(`Unable to compare versions: ${t}`),!1}}};class v{constructor({url:t,host:e,apiKey:o,https:r,prefix:n,port:a=6333,timeout:i=3e5,checkCompatibility:s=!0,...c}={}){if(this._https=r??"string"==typeof o,this._scheme=this._https?"https":"http",this._prefix=n??"",this._prefix.length>0&&!this._prefix.startsWith("/")&&(this._prefix=`/${this._prefix}`),t&&e)throw new _(`Only one of \`url\`, \`host\` params can be set. Url is ${t}, host is ${e}`);if(e&&(e.startsWith("http://")||e.startsWith("https://")||/:\d+$/.test(e)))throw new _("The `host` param is not expected to contain neither protocol (http:// or https://) nor port (:6333).\nTry to use the `url` parameter instead.");if(t){if(!t.startsWith("http://")&&!t.startsWith("https://"))throw new _("The `url` param expected to contain a valid URL starting with a protocol (http:// or https://).");const e=new URL(t);if(this._host=e.hostname,this._port=e.port?Number(e.port):a,this._scheme=e.protocol.replace(":",""),this._prefix.length>0&&"/"!==e.pathname)throw new _(`Prefix can be set either in \`url\` or in \`prefix\`.\nurl is ${t}, prefix is ${e.pathname}`)}else this._port=a,this._host=e??"127.0.0.1";const l=new Headers([["user-agent","qdrant-js/"+String(g)]]),h=c.headers??{};Object.keys(h).forEach((t=>{h[t]&&l.set(t,String(h[t]))})),"string"==typeof o&&("http"===this._scheme&&console.warn("Api key is used with unsecure connection."),l.set("api-key",o));const p=this._port?`${this._host}:${this._port}`:this._host;this._restUri=`${this._scheme}://${p}${this._prefix}`;const d=c.maxConnections,u={headers:l,timeout:i,connections:d};this._openApiClient=f(this._restUri,u),s&&this._openApiClient.root({}).then((t=>{const e=t.data.version;C.isCompatible(g,e)||console.warn(`Client version ${g} is incompatible with server version ${e}. Major versions should match and minor version difference must not exceed 1. Set checkCompatibility=false to skip version check.`)})).catch((()=>{console.warn("Failed to obtain server version. Unable to check client-server compatibility. Set checkCompatibility=false to skip version check.")}))}api(){return this._openApiClient}async searchBatch(t,{searches:e,consistency:r,timeout:n}){return o((await this._openApiClient.searchBatchPoints({collection_name:t,consistency:r,timeout:n,searches:e})).data.result).orThrow("Search batch returned empty")}async search(t,{shard_key:e,vector:r,limit:n=10,offset:a=0,filter:i,params:s,with_payload:c=!0,with_vector:l=!1,score_threshold:h,consistency:p,timeout:d}){return o((await this._openApiClient.searchPoints({collection_name:t,consistency:p,timeout:d,shard_key:e,vector:r,limit:n,offset:a,filter:i,params:s,with_payload:c,with_vector:l,score_threshold:h})).data.result).orThrow("Search returned empty")}async recommendBatch(t,{searches:e,consistency:r,timeout:n}){return o((await this._openApiClient.recommendBatchPoints({collection_name:t,searches:e,consistency:r,timeout:n})).data.result).orElse([])}async recommend_batch(t,{searches:e,consistency:r,timeout:n}){return o((await this._openApiClient.recommendBatchPoints({collection_name:t,searches:e,consistency:r,timeout:n})).data.result).orElse([])}async recommend(t,{shard_key:e,positive:r,negative:n,strategy:a,filter:i,params:s,limit:c=10,offset:l=0,with_payload:h=!0,with_vector:p=!1,score_threshold:d,using:u,lookup_from:m,consistency:_,timeout:y}){return o((await this._openApiClient.recommendPoints({collection_name:t,limit:c,shard_key:e,positive:r,negative:n,strategy:a,filter:i,params:s,offset:l,with_payload:h,with_vector:p,score_threshold:d,using:u,lookup_from:m,consistency:_,timeout:y})).data.result).orThrow("Recommend points API returned empty")}async scroll(t,{shard_key:e,filter:r,consistency:n,timeout:a,limit:i=10,offset:s,with_payload:c=!0,with_vector:l=!1,order_by:h}={}){return o((await this._openApiClient.scrollPoints({collection_name:t,shard_key:e,limit:i,offset:s,filter:r,with_payload:c,with_vector:l,order_by:h,consistency:n,timeout:a})).data.result).orThrow("Scroll points API returned empty")}async count(t,{shard_key:e,filter:r,exact:n=!0,timeout:a}={}){return o((await this._openApiClient.countPoints({collection_name:t,shard_key:e,filter:r,exact:n,timeout:a})).data.result).orThrow("Count points returned empty")}async collectionClusterInfo(t){return o((await this._openApiClient.collectionClusterInfo({collection_name:t})).data.result).orThrow("Collection cluster info returned empty")}async updateCollectionCluster(t,{timeout:e,...r}){return o((await this._openApiClient.updateCollectionCluster({collection_name:t,timeout:e,...r})).data.result).orThrow("Update collection cluster returned empty")}async updateVectors(t,{wait:e=!0,ordering:r,points:n,shard_key:a}){return o((await this._openApiClient.updateVectors({collection_name:t,wait:e,ordering:r,points:n,shard_key:a})).data.result).orThrow("Update vectors returned empty")}async deleteVectors(t,{wait:e=!0,ordering:r,points:n,filter:a,vector:i,shard_key:s}){return o((await this._openApiClient.deleteVectors({collection_name:t,wait:e,ordering:r,points:n,filter:a,vector:i,shard_key:s})).data.result).orThrow("Delete vectors returned empty")}async searchPointGroups(t,{consistency:e,timeout:r,shard_key:n,vector:a,filter:i,params:s,with_payload:c=null,with_vector:l=null,score_threshold:h,group_by:p,group_size:d,limit:u}){return o((await this._openApiClient.searchPointGroups({collection_name:t,consistency:e,timeout:r,shard_key:n,vector:a,filter:i,params:s,with_payload:c,with_vector:l,score_threshold:h,group_by:p,group_size:d,limit:u})).data.result).orThrow("Search point groups returned empty")}async recommendPointGroups(t,{consistency:e,timeout:r,shard_key:n,positive:a,strategy:i,negative:s=[],filter:c,params:l,with_payload:h=null,with_vector:p=null,score_threshold:d,using:u=null,lookup_from:m=null,group_by:_,group_size:y,limit:w}){return o((await this._openApiClient.recommendPointGroups({collection_name:t,consistency:e,timeout:r,shard_key:n,positive:a,negative:s,strategy:i,filter:c,params:l,with_payload:h,with_vector:p,score_threshold:d,using:u,lookup_from:m,group_by:_,group_size:y,limit:w})).data.result).orThrow("Recommend point groups API returned empty")}async upsert(t,{wait:e=!0,ordering:r,...n}){return o((await this._openApiClient.upsertPoints({collection_name:t,wait:e,ordering:r,...n})).data.result).orThrow("Upsert returned empty")}async retrieve(t,{shard_key:e,ids:r,with_payload:n=!0,with_vector:a,consistency:i,timeout:s}){return o((await this._openApiClient.getPoints({collection_name:t,shard_key:e,ids:r,with_payload:n,with_vector:a,consistency:i,timeout:s})).data.result).orThrow("Retrieve API returned empty")}async delete(t,{wait:e,ordering:r,...n}){return o((await this._openApiClient.deletePoints({collection_name:t,wait:e,ordering:r,...n})).data.result).orThrow("Delete points returned empty")}async setPayload(t,{payload:e,points:r,filter:n,shard_key:a,key:i,ordering:s,wait:c=!0}){return o((await this._openApiClient.setPayload({collection_name:t,payload:e,points:r,filter:n,shard_key:a,key:i,wait:c,ordering:s})).data.result).orThrow("Set payload returned empty")}async overwritePayload(t,{ordering:e,payload:r,points:n,filter:a,shard_key:i,key:s,wait:c=!0}){return o((await this._openApiClient.overwritePayload({collection_name:t,payload:r,points:n,filter:a,shard_key:i,key:s,wait:c,ordering:e})).data.result).orThrow("Overwrite payload returned empty")}async deletePayload(t,{ordering:e,keys:r,points:n,filter:a,shard_key:i,wait:s=!0}){return o((await this._openApiClient.deletePayload({collection_name:t,keys:r,points:n,filter:a,shard_key:i,wait:s,ordering:e})).data.result).orThrow("Delete payload returned empty")}async clearPayload(t,{ordering:e,wait:r=!0,...n}){return o((await this._openApiClient.clearPayload({collection_name:t,wait:r,ordering:e,...n})).data.result).orThrow("Clear payload returned empty")}async updateCollectionAliases({actions:t,timeout:e}){return o((await this._openApiClient.updateAliases({actions:t,timeout:e})).data.result).orThrow("Update aliases returned empty")}async getCollectionAliases(t){return o((await this._openApiClient.getCollectionAliases({collection_name:t})).data.result).orThrow("Get collection aliases returned empty")}async getAliases(){return o((await this._openApiClient.getCollectionsAliases({})).data.result).orThrow("Get aliases returned empty")}async getCollections(){return o((await this._openApiClient.getCollections({})).data.result).orThrow("Get collections returned empty")}async getCollection(t){return o((await this._openApiClient.getCollection({collection_name:t})).data.result).orThrow("Get collection returned empty")}async updateCollection(t,e){return o((await this._openApiClient.updateCollection({collection_name:t,...e})).data.result).orThrow("Update collection returned empty")}async deleteCollection(t,e){return o((await this._openApiClient.deleteCollection({collection_name:t,...e})).data.result).orThrow("Delete collection returned empty")}async createCollection(t,{timeout:e,vectors:r,hnsw_config:n,init_from:a,on_disk_payload:i,optimizers_config:s,quantization_config:c,replication_factor:l,shard_number:h,sharding_method:p,wal_config:d,write_consistency_factor:u,sparse_vectors:m,strict_mode_config:_}){return o((await this._openApiClient.createCollection({collection_name:t,timeout:e,vectors:r,hnsw_config:n,init_from:a,on_disk_payload:i,optimizers_config:s,quantization_config:c,replication_factor:l,shard_number:h,sharding_method:p,wal_config:d,write_consistency_factor:u,sparse_vectors:m,strict_mode_config:_})).data.result).orThrow("Create collection returned empty")}async recreateCollection(t,{timeout:e,vectors:r,hnsw_config:n,init_from:a,on_disk_payload:i,optimizers_config:s,quantization_config:c,replication_factor:l,shard_number:h,sharding_method:p,wal_config:d,write_consistency_factor:u,sparse_vectors:m,strict_mode_config:_}){o(await this._openApiClient.deleteCollection({collection_name:t,timeout:e})).get("ok").orThrow("Delete collection returned failed");return o(await this._openApiClient.createCollection({collection_name:t,timeout:e,vectors:r,hnsw_config:n,init_from:a,on_disk_payload:i,optimizers_config:s,quantization_config:c,replication_factor:l,shard_number:h,sharding_method:p,wal_config:d,write_consistency_factor:u,sparse_vectors:m,strict_mode_config:_})).orThrow("Create collection returned empty")}async createPayloadIndex(t,{wait:e,ordering:r,field_name:n,field_schema:a}){return o((await this._openApiClient.createFieldIndex({collection_name:t,field_name:n,field_schema:a,wait:e,ordering:r})).data.result).orThrow("Create field index returned empty")}async deletePayloadIndex(t,e,{wait:r=!0,ordering:n}={}){return o((await this._openApiClient.deleteFieldIndex({collection_name:t,field_name:e,wait:r,ordering:n})).data.result).orThrow("Delete field index returned empty")}async listSnapshots(t){return o((await this._openApiClient.listSnapshots({collection_name:t})).data.result).orThrow("List snapshots API returned empty")}async createSnapshot(t,e){return o((await this._openApiClient.createSnapshot({collection_name:t,...e})).data.result).orNull()}async deleteSnapshot(t,e,r){return o((await this._openApiClient.deleteSnapshot({collection_name:t,snapshot_name:e,...r})).data.result).orThrow("Delete snapshot API returned empty")}async listFullSnapshots(){return o((await this._openApiClient.listFullSnapshots({})).data.result).orThrow("List full snapshots API returned empty")}async createFullSnapshot(t){return o((await this._openApiClient.createFullSnapshot(t??{})).data.result).orThrow("Create full snapshot API returned empty")}async deleteFullSnapshot(t,e){return o((await this._openApiClient.deleteFullSnapshot({snapshot_name:t,...e})).data.result).orThrow("Delete full snapshot API returned empty")}async recoverSnapshot(t,{location:e,priority:r,checksum:n,api_key:a}){return o((await this._openApiClient.recoverFromSnapshot({collection_name:t,location:e,priority:r,checksum:n,api_key:a})).data.result).orThrow("Recover from snapshot API returned empty")}async lockStorage(t){return o((await this._openApiClient.postLocks({write:!0,error_message:t})).data.result).orThrow("Lock storage returned empty")}async unlockStorage(){return o((await this._openApiClient.postLocks({write:!1})).data.result).orThrow("Post locks returned empty")}async getLocks(){return o((await this._openApiClient.getLocks({})).data.result).orThrow("Get locks returned empty")}async batchUpdate(t,{wait:e=!0,ordering:r,...n}){return o((await this._openApiClient.batchUpdate({collection_name:t,wait:e,ordering:r,...n})).data.result).orThrow("Batch update returned empty")}async recoverShardFromSnapshot(t,e,{wait:r=!0,...n}){return o((await this._openApiClient.recoverShardFromSnapshot({collection_name:t,shard_id:e,wait:r,...n})).data.result).orThrow("Recover shard from snapshot returned empty")}async listShardSnapshots(t,e){return o((await this._openApiClient.listShardSnapshots({collection_name:t,shard_id:e})).data.result).orThrow("List shard snapshots returned empty")}async createShardSnapshot(t,e,{wait:r=!0}){return o((await this._openApiClient.createShardSnapshot({collection_name:t,shard_id:e,wait:r})).data.result).orThrow("Create shard snapshot returned empty")}async deleteShardSnapshot(t,e,r,{wait:n=!0}){return o((await this._openApiClient.deleteShardSnapshot({collection_name:t,shard_id:e,snapshot_name:r,wait:n})).data.result).orThrow("Create shard snapshot returned empty")}async createShardKey(t,{shard_key:e,shards_number:r,replication_factor:n,placement:a,timeout:i}){return o((await this._openApiClient.createShardKey({collection_name:t,shard_key:e,shards_number:r,replication_factor:n,placement:a,timeout:i})).data.result).orThrow("Create shard key returned empty")}async deleteShardKey(t,{shard_key:e,timeout:r}){return o((await this._openApiClient.deleteShardKey({collection_name:t,shard_key:e,timeout:r})).data.result).orThrow("Create shard key returned empty")}async discoverPoints(t,{consistency:e,timeout:r,shard_key:n,target:a,context:i,params:s,limit:c,offset:l,with_payload:h,with_vector:p,using:d,lookup_from:u}){return o((await this._openApiClient.discoverPoints({collection_name:t,consistency:e,timeout:r,shard_key:n,target:a,context:i,params:s,limit:c,offset:l,with_payload:h,with_vector:p,using:d,lookup_from:u})).data.result).orThrow("Discover points returned empty")}async discoverBatchPoints(t,{consistency:e,timeout:r,searches:n}){return o((await this._openApiClient.discoverBatchPoints({collection_name:t,consistency:e,timeout:r,searches:n})).data.result).orThrow("Discover batch points returned empty")}async versionInfo(){return o((await this._openApiClient.root({})).data).orThrow("Version Info returned empty")}async collectionExists(t){return o((await this._openApiClient.collectionExists({collection_name:t})).data.result).orThrow("Collection exists returned empty")}async query(t,{consistency:e,timeout:r,shard_key:n,prefetch:a,query:i,using:s,filter:c,params:l,score_threshold:h,limit:p,offset:d,with_vector:u,with_payload:m,lookup_from:_}){return o((await this._openApiClient.queryPoints({collection_name:t,consistency:e,timeout:r,shard_key:n,prefetch:a,query:i,using:s,filter:c,params:l,score_threshold:h,limit:p,offset:d,with_vector:u,with_payload:m,lookup_from:_})).data.result).orThrow("Query points returned empty")}async queryBatch(t,{consistency:e,timeout:r,searches:n}){return o((await this._openApiClient.queryBatchPoints({collection_name:t,consistency:e,timeout:r,searches:n})).data.result).orThrow("Query points returned empty")}async queryGroups(t,{consistency:e,timeout:r,shard_key:n,prefetch:a,query:i,using:s,filter:c,params:l,score_threshold:h,with_vector:p,with_payload:d,group_by:u,group_size:m,limit:_,with_lookup:y}){return o((await this._openApiClient.queryPointsGroups({collection_name:t,consistency:e,timeout:r,shard_key:n,prefetch:a,query:i,using:s,filter:c,params:l,score_threshold:h,with_vector:p,with_payload:d,group_by:u,group_size:m,limit:_,with_lookup:y})).data.result).orThrow("Query groups returned empty")}async facet(t,{consistency:e,timeout:r,shard_key:n,key:a,limit:i,filter:s,exact:c}){return o((await this._openApiClient.facet({collection_name:t,consistency:e,timeout:r,shard_key:n,key:a,limit:i,filter:s,exact:c})).data.result).orThrow("Facet returned empty")}async searchMatrixPairs(t,{consistency:e,timeout:r,shard_key:n,filter:a,sample:i,limit:s,using:c}){return o((await this._openApiClient.searchMatrixPairs({collection_name:t,consistency:e,timeout:r,shard_key:n,filter:a,sample:i,limit:s,using:c})).data.result).orThrow("Search points matrix pairs returned empty")}async searchMatrixOffsets(t,{consistency:e,timeout:r,shard_key:n,filter:a,sample:i,limit:s,using:c}){return o((await this._openApiClient.searchMatrixOffsets({collection_name:t,consistency:e,timeout:r,shard_key:n,filter:a,sample:i,limit:s,using:c})).data.result).orThrow("Search points matrix offsets returned empty")}}export{v as QdrantClient,_ as QdrantClientConfigError,w as QdrantClientResourceExhaustedError,y as QdrantClientTimeoutError,m as QdrantClientUnexpectedResponseError};
