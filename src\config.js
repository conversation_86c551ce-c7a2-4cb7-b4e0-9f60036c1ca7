import 'dotenv/config';

const required = (value, name) => {
  if (!value) {
    throw new Error('Missing required env var: ' + name);
  }
  return value;
};

const optionalNumber = (value, fallback) => {
  const parsed = Number(value);
  return Number.isFinite(parsed) ? parsed : fallback;
};

export const config = {
  whatsapp: {
    sessionPath: process.env.WHATSAPP_SESSION_DIR || './session',
    clientId: process.env.WHATSAPP_CLIENT_ID || 'primary',
  },
  telegram: {
    token: required(process.env.TELEGRAM_BOT_TOKEN, 'TELEGRAM_BOT_TOKEN'),
    chatId: required(process.env.TELEGRAM_CHAT_ID, 'TELEGRAM_CHAT_ID'),
  },
  ai: {
    baseUrl: process.env.LM_STUDIO_URL || 'http://127.0.0.1:1234/v1',
    chatEndpoint: process.env.LM_STUDIO_CHAT_ENDPOINT || '/chat/completions',
    model: process.env.LM_STUDIO_MODEL || 'local-model',
    temperature: optionalNumber(process.env.LM_STUDIO_TEMPERATURE, 0.2),
    maxTokens: optionalNumber(process.env.LM_STUDIO_MAX_TOKENS, 512),
    embeddingEndpoint: process.env.LM_STUDIO_EMBED_ENDPOINT || '/embeddings',
    embeddingModel: process.env.LM_STUDIO_EMBED_MODEL || process.env.LM_STUDIO_MODEL || 'local-embedder',
  },
  qdrant: {
    url: process.env.QDRANT_URL || 'http://127.0.0.1:6333',
    apiKey: process.env.QDRANT_API_KEY || '',
    collection: process.env.QDRANT_COLLECTION || 'whatsapp_messages',
    vectorSize: optionalNumber(process.env.QDRANT_VECTOR_SIZE, 768),
    distance: process.env.QDRANT_DISTANCE || 'Cosine',
    queryLimit: optionalNumber(process.env.QDRANT_QUERY_LIMIT, 6),
  },
  persistence: {
    messageStorePath: process.env.MESSAGE_STORE_PATH || './data/messages.json',
    maxMessagesPerChat: optionalNumber(process.env.MAX_MESSAGES_PER_CHAT, 20),
  },
  features: {
    autoReply: process.env.AUTO_REPLY === 'true',
    summaryHourUTC: optionalNumber(process.env.SUMMARY_HOUR_UTC, 20),
    telegramAssistant: process.env.TELEGRAM_ASSISTANT !== 'false',
    ragEnabled: process.env.RAG_ENABLED !== 'false',
  },
};
